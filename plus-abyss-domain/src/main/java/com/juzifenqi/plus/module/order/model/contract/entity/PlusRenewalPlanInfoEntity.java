package com.juzifenqi.plus.module.order.model.contract.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/8/19  9:56
 * @description
 */
@Data
public class PlusRenewalPlanInfoEntity implements Serializable {

    /**
     * 用户id
     */
    private Integer userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 渠道名称
     */
    private String channel;

    /**
     * 月卡编号
     */
    private String monthNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 续费计划列表
     */
    private List<PlusMonthMemberRenewalPlanEntity> renewalPlanList;

}
