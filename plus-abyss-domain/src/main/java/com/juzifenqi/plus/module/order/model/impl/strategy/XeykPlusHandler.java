package com.juzifenqi.plus.module.order.model.impl.strategy;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.enumeration.OrderChannelEnum;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.DeductOptSateEnum;
import com.juzifenqi.plus.enums.DeductResultStateEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.OrderRemindStateEnum;
import com.juzifenqi.plus.enums.PlusDeductLogTypeEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderRelationBusinessType;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.enums.PlusSmsSendNodeEnum;
import com.juzifenqi.plus.enums.PlusSwitchEnum;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.enums.refund.CancelBusinessSceneEnum;
import com.juzifenqi.plus.enums.supplier.SupplierTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusCashbackRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoDetailRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusProgramCashbackSnapshotEntity;
import com.juzifenqi.plus.module.common.IMemberSwitchControlRepository;
import com.juzifenqi.plus.module.common.ISmsRepository;
import com.juzifenqi.plus.module.common.entity.MemberPlusSwitchControlEntity;
import com.juzifenqi.plus.module.common.event.PlusSmsParamEvent;
import com.juzifenqi.plus.module.order.model.PlusAfterOrderRemindModel;
import com.juzifenqi.plus.module.order.model.contract.IMemberPlusRenewPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderDeductPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRelationRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberPlusRenewPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusAfterOrderRemindEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductResEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.converter.IPlusOrderModelConverter;
import com.juzifenqi.plus.module.order.model.event.CreateDeductPlanEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusPayDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRealDeductEvent;
import com.juzifenqi.plus.module.order.model.event.order.LoanOrderCloseEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderCancelExtInfoEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderDeductCallBackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayDeductRespEvent;
import com.juzifenqi.plus.utils.PlusConcatUtils;
import com.juzifenqi.plus.utils.RedisUtils;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 小额月卡-策略处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/09/11 13:45
 */
@Slf4j
@Component
public class XeykPlusHandler extends AbstractStrategyHandler {

    @Autowired
    private IPlusOrderRelationRepository    plusOrderRelationRepository;
    @Autowired
    private IMemberSwitchControlRepository  memberSwitchControlRepository;
    @Autowired
    private IPlusOrderRepository            plusOrderRepository;
    @Autowired
    private PlusAfterOrderRemindModel       remindModel;
    @Autowired
    private IMemberPlusInfoDetailRepository memberPlusInfoDetailRepository;
    @Autowired
    private IPlusOrderDeductPlanRepository  plusOrderDeductPlanRepository;
    @Autowired
    private RedisUtils                      redisUtils;
    @Autowired
    private IMemberPlusCashbackRepository   memberPlusCashbackRepository;
    @Autowired
    private IPlusOrderDeductPlanRepository  deductPlanRepository;
    @Autowired
    private IMemberPlusRenewPlanRepository  renewPlanRepository;
    @Autowired
    private ISmsRepository                  smsRepository;

    private final IPlusOrderModelConverter modelConverter = IPlusOrderModelConverter.instance;


    /**
     * 小额月卡划扣支持的渠道
     */
    private static final List<Integer> DEDUCT_CHANNEL_LIST = Collections.singletonList(
            OrderChannelEnum.现金贷.getChannelCode());

    /**
     * 创建划扣计划
     */
    @Override
    public void createDuctPlan(CreateDeductPlanEvent event) {
        log.info("小额月卡保存划扣计划开始：{}", JSON.toJSONString(event));
        PlusOrderDeductPlanEntity plusOrderDeductPlanPo = modelConverter.toDeductPlanEntity(event);
        //计算
        plusOrderDeductPlanPo.setMsgPlanTime(new Date());
        //小额月卡不发短信，默认设置成1
        plusOrderDeductPlanPo.setMsgStatus(1);
        plusOrderDeductPlanPo.setOptStatus(0);
        plusOrderDeductPlanPo.setDeductNum(0);
        plusOrderDeductPlanPo.setRemark("小额月卡-指定日期划扣");
        plusOrderDeductPlanRepository.create(plusOrderDeductPlanPo);
        log.info("小额月卡保存划扣计划结束：plusOrderSn:{}", event.getPlusOrderSn());
    }


    /**
     * 划扣前置处理 TODO 数据封装-提出
     */
    @Override
    public void activeDeductPreHandle(PlusDeductEvent param) {
        log.info("小额月卡-划扣前置处理开始,param={}", JSON.toJSONString(param));
        Integer userId = param.getUserId();
        PlusPayTypeEnum deductFlag = param.getDeductFlag();
        switch (deductFlag) {
            case PAY_TYPE_1:
                String loanOrderSn = param.getOrderSn();
                Integer configId = param.getConfigId();
                log.info("小额月卡-后付款待支付订单自动划扣开始-userId={},放款订单号={}", userId,
                        loanOrderSn);
                //1、借款渠道判断
                if (!DEDUCT_CHANNEL_LIST.contains(param.getOrderChannelId())) {
                    log.info("小额月卡-后付款自动划扣,当前渠道不处理,userId:{}", userId);
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_24.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_24.getName());
                }
                //2、开关是否开启
                MemberPlusSwitchControlEntity switchControl = memberSwitchControlRepository.getSwitchByCode(
                        PlusSwitchEnum.XEYK_AUTOMATIC_DEDUCT.getCode());
                if (switchControl == null || switchControl.getStatus() == CommonConstant.TWO) {
                    log.info("小额月卡-自动划扣开关未开启-userId={},订单号={}", userId,
                            loanOrderSn);
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_21.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_21.getName());
                }
                param.setSwitchControl(switchControl);
                //3、获取用户会员未过期 & 未支付 & 后付款信息
                PlusOrderEntity orderInfo = orderQueryModel.getUserWaitPayOrder(userId,
                        configId);
                log.info("小额月卡-后付款待支付订单-userId={},info={}", userId,
                        JSON.toJSONString(orderInfo));
                if (orderInfo == null) {
                    log.info("小额月卡-未查询到后付款待支付订单-userId={},放款订单号={}", userId,
                            loanOrderSn);
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_22.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_22.getName());
                }
                param.setPlusOrderEntity(orderInfo);
                String plusOrderSn = orderInfo.getOrderSn();
                //检查是否首次开通
                MemberPlusInfoDetailEntity infoDetail = memberPlusInfoDetailRepository.getByOrderSn(
                        plusOrderSn);
                if (infoDetail == null) {
                    log.info("小额月卡-查询会员信息失败-userId={},plusOrderSn={}", userId,
                            plusOrderSn);
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_33.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_33.getName());
                }
                param.setPlusType(infoDetail.getPlusType());
                // 20231214 zjf 更换首单判断方式
                MemberPlusRenewPlanEntity renewPlan = renewPlanRepository.getByPlusOrderSn(
                        plusOrderSn);
                log.info("小额月卡-后付款订单自动划扣获取续费计划：{}",
                        JSON.toJSONString(renewPlan));
                boolean firstOrder = renewPlan == null || StringUtils.equals(plusOrderSn,
                        renewPlan.getGroupId());
                if (firstOrder) {
                    //首次开通逻辑
                    //校验绑定
                    PlusOrderRelationEntity relationEntity = plusOrderRelationRepository.getByBusinessTypeAndPlusOrderSn(
                            PlusOrderRelationBusinessType.FXK.getCode(), plusOrderSn);
                    if (relationEntity == null || !relationEntity.getOrderSn()
                            .equals(loanOrderSn)) {
                        log.info("小额月卡-首次开通未绑定-userId={},放款订单号={},plusOrderSn={}",
                                userId, loanOrderSn, plusOrderSn);
                        throw new PlusAbyssException(
                                PlusDeductLogTypeEnum.LOG_TYPE_26.getCode().toString(),
                                PlusDeductLogTypeEnum.LOG_TYPE_26.getName());
                    }
                } else {
                    //检查续费划扣是否失败
                    PlusOrderDeductPlanEntity infoByPlusOrderSn = plusOrderDeductPlanRepository.getInfoByPlusOrderSn(
                            plusOrderSn);
                    if (infoByPlusOrderSn != null
                            && infoByPlusOrderSn.getOptStatus() != CommonConstant.THREE) {
                        //非续费划扣失败
                        log.info("小额月卡-续费划扣-非失败-userId={},放款订单号={},plusOrderSn={}",
                                userId, loanOrderSn, plusOrderSn);
                        throw new PlusAbyssException(
                                PlusDeductLogTypeEnum.LOG_TYPE_27.getCode().toString(),
                                PlusDeductLogTypeEnum.LOG_TYPE_27.getName());
                    }
                }
                log.info("小额月卡-放款划扣前置处理结束,param={}", JSON.toJSONString(param));
                break;
            case PAY_TYPE_7:
                PlusOrderEntity order = plusOrderRepository.getByPlusOrderSn(
                        param.getPlusOrderSn());
                log.info("小额月卡-延迟后付款待支付订单-userId={},plusOrderSn={},info={}", userId,
                        param.getPlusOrderSn(), JSON.toJSONString(order));
                if (order == null) {
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_28.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_28.getName());
                }
                if (PlusOrderStateEnum.WAIT_PAY.getCode() != order.getOrderState()) {
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_29.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_29.getName());
                }
                if (!PlusOrderPayTypeEnum.PAY_AFTER.getValue().equals(order.getPayType())) {
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_30.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_30.getName());
                }
                if (order.getEndTime().before(new Date())) {
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_31.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_31.getName());
                }
                param.setPlusOrderEntity(order);
                //开关是否开启
                MemberPlusSwitchControlEntity control = memberSwitchControlRepository.getSwitchByCode(
                        PlusSwitchEnum.XEYK_AUTOMATIC_DEDUCT.getCode());
                if (control == null || control.getStatus() == CommonConstant.TWO) {
                    throw new PlusAbyssException(
                            PlusDeductLogTypeEnum.LOG_TYPE_21.getCode().toString(),
                            PlusDeductLogTypeEnum.LOG_TYPE_21.getName());
                }
                param.setSwitchControl(control);
                log.info("小额月卡-任务划扣前置处理结束,param={}", JSON.toJSONString(param));
                break;
            default:
                log.info("小额月卡-划扣未找到正确类型-userId={},param={}", userId,
                        JSON.toJSONString(param));
                break;
        }
    }


    @Override
    public PlusOrderDeductResEntity deduct(PlusDeductEvent deductPlan) {
        log.info("小额月卡划扣申请开始：{}", JSON.toJSONString(deductPlan));
        Integer userId = deductPlan.getUserId();
        PlusOrderEntity orderInfo = deductPlan.getPlusOrderEntity();
        String plusOrderSn = orderInfo.getOrderSn();
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        try {
            // 获取指定卡划扣申请
            PlusOrderDeductResEntity getResEntity = getDeductApplyEventByDefaultCard(orderInfo,
                    deductPlan);
            if (!DeductResultStateEnum.SUCCESS.getCode().equals(getResEntity.getOptStatus())) {
                return getResEntity;
            }
            resEntity.setSeparateEntity(getResEntity.getSeparateEntity());
            // 组装划扣数据
            PlusPayDeductEvent deductEvent = converter.toPlusPayDeductEvent(
                    getResEntity.getSeparateEntity(), null);
            getResEntity.getSeparateEntity().getItems().forEach(item -> {
                // 只放清分主体
                if (item.getSupplierType().equals(SupplierTypeEnum.QF.getCode())) {
                    deductEvent.getSplitItems().add(converter.toPlusSplitItemEvent(item));
                }
            });
            PlusOrderPayDeductRespEvent respEvent = fmsRepository.deduct(deductEvent);
            resEntity.setDeductRespEvent(respEvent);
            if (!Objects.isNull(respEvent) && PayStateCodeEnum.I.getCode()
                    .equals(respEvent.getState())) {
                //成功
                log.info("放款划扣申请成功,plusOrderSn{}", plusOrderSn);
                resEntity.setRemark("放款划扣申请成功");
                resEntity.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
            } else {
                //失败
                log.info("放款划扣申请失败,plusOrderSn:{}", plusOrderSn);
                resEntity.setRemark("放款划扣申请返回失败");
                resEntity.setOptStatus(DeductResultStateEnum.FAIL.getCode());
            }
        } catch (Exception e) {
            log.info("后付款会员划扣申请异常,plusOrderSn:{}", plusOrderSn, e);
            resEntity.setRemark("后付款会员划扣申请异常");
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            LogUtil.printLog("后付款待支付订单自动划扣申请结束出现未知异常：", e);
        }
        log.info("小额月卡划扣申请完成：{}，{}，{}", userId, plusOrderSn, deductPlan.getOrderSn());
        return resEntity;
    }

    @Override
    @Deprecated
    public PlusOrderDeductResEntity deductOld(PlusDeductEvent deductPlan) {
        Integer userId = deductPlan.getUserId();
        PlusOrderEntity orderInfo = deductPlan.getPlusOrderEntity();
        String plusOrderSn = orderInfo.getOrderSn();
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        try {
            PlusRealDeductEvent realDeductEvent = new PlusRealDeductEvent();
            realDeductEvent.setMemberId(userId);
            realDeductEvent.setPlusOrderSn(plusOrderSn);
            realDeductEvent.setBankId(deductPlan.getBankId());
            realDeductEvent.setOrderAmount(orderInfo.getOrderAmount());
            PlusOrderDeductCallBackEvent payOrderVo = payExternalRepository.deduct(realDeductEvent);
            resEntity.setCallBackEvent(payOrderVo);
            if (!Objects.isNull(payOrderVo) && "S".equals(payOrderVo.getStatus())) {
                //成功
                log.info("放款划扣成功,plusOrderSn{}", plusOrderSn);
                resEntity.setRemark("放款划扣成功");
                resEntity.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
            } else {
                //失败
                log.info("放款划扣失败,plusOrderSn:{}", plusOrderSn);
                resEntity.setRemark("放款划扣返回失败");
                resEntity.setOptStatus(DeductResultStateEnum.FAIL.getCode());
            }
        } catch (Exception e) {
            log.info("后付款会员划扣异常,plusOrderSn:{}", plusOrderSn, e);
            resEntity.setRemark("后付款会员划扣异常");
            resEntity.setOptStatus(DeductResultStateEnum.ERROR.getCode());
            LogUtil.printLog("后付款待支付订单自动划扣结束出现未知异常：", e);
        }
        return resEntity;
    }

    @Override
    public void createOrderAfter(PlusOrderEntity plusOrderEntity,
            PlusOrderCreateEvent createEvent) {
        String orderSn = plusOrderEntity.getOrderSn();
        log.info("小额月卡会员订单扩展信息保存：{}", orderSn);
        // 分流信息
        orderShuntRepository.saveOrderShunt(plusOrderEntity, createEvent);
        // 结清返现权益快照
        PlusProgramCashbackSnapshotEntity snapshotEntity = modelConverter.toplusProgramCashbackSnapshotEntity(
                createEvent, plusOrderEntity, PlusModelEnum.JQFX.getModelId());
        memberPlusCashbackRepository.savePlusCashbackSnapshot(snapshotEntity);
        // 如果是全款单先付款的存储借款订单关联关系到缓存，支付成功回调后落库数据
        PlusOrderPayTypeEnum payTypeEnum = PlusOrderPayTypeEnum.getByValue(
                createEvent.getPayType());
        if (payTypeEnum == null) {
            throw new PlusAbyssException("付款类型无效");
        }
        if (createEvent.getCreateOrderContext() != null && StringUtils.isNotBlank(
                createEvent.getCreateOrderContext().getLoanOrderSn())
                && !payTypeEnum.getPayAfter()) {
            String key = PlusConcatUtils.symbolBelowStr(RedisConstantPrefix.PLUS_RELATION,
                    plusOrderEntity.getOrderSn(), PlusOrderRelationBusinessType.FXK.getCode());
            log.info("全款创单成功绑定借款单与会员单关系缓存key：{}", key);
            redisUtils.setEx(key, createEvent.getCreateOrderContext().getLoanOrderSn(), 1,
                    TimeUnit.HOURS);
        }
    }

    @Override
    public void cancelOrderAfter(PlusOrderEntity plusOrder, PlusOrderCancelEvent event) {
        log.info("小额月卡取消会员后置逻辑处理：{}", plusOrder.getOrderSn());
        //取消划扣计划
        deductPlanRepository.changeDeductPlanState(plusOrder.getOrderSn(),
                DeductOptSateEnum.INVALID.getCode());
        // 小额月卡放款划扣-首单划扣失败,短信节点:立即划扣-划扣失败
        OrderCancelExtInfoEvent extInfo = event.getExtInfo();
        if (extInfo != null) {
            CancelBusinessSceneEnum sceneEnum = CancelBusinessSceneEnum.getByCode(
                    extInfo.getScene());
            if (sceneEnum == null) {
                log.info("小额月卡取消会员后置逻辑处理,未知场景");
                return;
            }
            switch (sceneEnum) {
                case ONE:
                    // 发送短信
                    PlusSmsParamEvent plusSmsParamVo = converter.toPlusSmsParamEvent(plusOrder,
                            PlusSmsSendNodeEnum.NOD_13.getCode(), event.getLoanOrderSn());
                    smsRepository.sendSmsByConfig(plusSmsParamVo);
                    log.info(
                            "小额月卡-放款成功-划扣申请失败，发送划扣失败短信结束-userId={},订单号={},会员订单号={}",
                            plusOrder.getUserId(), event.getLoanOrderSn(), plusOrder.getOrderSn());
                    break;
                case TWO:
                    //判断的商城
                    Integer channelId = event.getChannelId() == null ? 1 : event.getChannelId();
                    PlusSmsParamEvent smsParam = converter.toPlusSmsParamEvent(plusOrder, channelId,
                            PlusSmsSendNodeEnum.NOD_16.getCode(), extInfo.getBankId(),
                            event.getLoanOrderSn());
                    smsRepository.sendSmsByConfig(smsParam);
                    log.info(
                            "小额月卡-放款成功-划扣申请失败满6次,发送划扣失败短信结束-userId={},订单号={},会员订单号={}",
                            plusOrder.getUserId(), event.getLoanOrderSn(), plusOrder.getOrderSn());
                    break;
                default:
                    break;

            }
        }
    }

    @Override
    public void loanFkClose(LoanOrderCloseEvent event) {
        log.info("小额月卡风控闭单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.XEYK_SPEED_REFUND.getCode());
        log.info("小额月卡风控闭单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

    @Override
    public void loanZpClose(LoanOrderCloseEvent event) {
        log.info("小额月卡资匹闭单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.XEYK_SPEED_REFUND.getCode());
        log.info("小额月卡资匹闭单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

    @Override
    public void afterOrderSendMsgAndPush(PlusAfterOrderRemindEntity entity) {
        String groupId = entity.getOrderSn();
        PlusOrderEntity groupOrder = plusOrderRepository.getByPlusOrderSn(groupId);
        if (groupOrder == null) {
            log.info("续费前5天发短信-groupOrder为空，groupId：{}", groupId);
            return;
        }
        MemberPlusRenewPlanEntity currentRenewPlan = renewPlanRepository.getCurrentRenewPlan(
                groupId);
        if (currentRenewPlan == null) {
            log.info("续费前5天发短信-未查询到续费计划，groupId：{}", groupId);
            return;
        }
        String plusOrderSn = currentRenewPlan.getPlusOrderSn();
        PlusOrderEntity plusOrderInfo = plusOrderRepository.getByPlusOrderSn(plusOrderSn);
        if (plusOrderInfo != null
                && plusOrderInfo.getOrderState() == PlusOrderStateEnum.PAY_SUCCESS.getCode()) {
            log.info("续费前5天发短信-提醒短信对应订单已支付成功，groupId：{}，plusOrderSn：{}",
                    groupId, plusOrderSn);
            return;
        }
        PlusSmsParamEvent plusSmsParamVo = new PlusSmsParamEvent();
        plusSmsParamVo.setOrderSn(plusOrderSn);
        plusSmsParamVo.setUserId(groupOrder.getUserId());
        plusSmsParamVo.setConfigId(JuziPlusEnum.XEYK_CARD.getCode());
        plusSmsParamVo.setChannelId(groupOrder.getChannelId());
        plusSmsParamVo.setSendNode(PlusSmsSendNodeEnum.NOD_18.getCode());
        plusSmsParamVo.setRenewAmount(groupOrder.getOrderAmount());
        plusSmsParamVo.setName(groupOrder.getProgramName());
        plusSmsParamVo.setProgramId(groupOrder.getProgramId());
        plusSmsParamVo.setOrderAmount(groupOrder.getOrderAmount());
        smsRepository.sendSmsByConfig(plusSmsParamVo);
        //修改状态
        entity.setState(OrderRemindStateEnum.DONE.getCode());
        remindModel.updatePlusAfterOrderRemindByOrderSn(entity);
    }

    @Override
    public void loanClose(LoanOrderCloseEvent event) {
        log.info("小额月卡取消订单处理开始：{}", JSON.toJSONString(event));
        createRapidPlusRefundRecord(event, PlusSwitchEnum.XEYK_SPEED_REFUND.getCode());
        log.info("小额月卡取消订单处理完成：{},{}", event.getUserId(), event.getLoanOrderSn());
    }

}
