package com.juzifenqi.plus.module.order.model.impl;

import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapshtoQueryModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSnapshotRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderModelSnapshotEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderProgramSnapshotEntity;
import com.juzifenqi.plus.module.order.model.impl.factory.converter.IPlusOrderSnapModelConverter;
import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 保存会员方案-快照
 */
@Service
@Slf4j
public class PlusOrderSnapshtoQueryModelImpl implements PlusOrderSnapshtoQueryModel {

    @Autowired
    private IPlusOrderSnapshotRepository orderProgramSnapRepository;
    @Autowired
    private IPlusProModelRepository      plusProModelRepository;
    @Autowired
    private IPlusProgramRepository       plusProgramRepository;

    private final IPlusOrderSnapModelConverter converter = IPlusOrderSnapModelConverter.instance;

    @Override
    public PlusOrderProgramSnapshotEntity getOrderProgramSnapshot(String plusOrderSn) {
        return orderProgramSnapRepository.getProgramSnapshotByOrderSn(plusOrderSn);
    }

    @Override
    public List<PlusOrderModelSnapshotEntity> listModelSnapshotByOrderSnBySort(String plusOrderSn) {
        return orderProgramSnapRepository.listModelSnapshotByOrderSnBySort(plusOrderSn);
    }

    @Override
    public PlusOrderModelSnapshotEntity getModelSnapshotByOrderSnAndModelId(String orderSn,
            Integer modelId) {
        PlusOrderModelSnapshotEntity entity = orderProgramSnapRepository.getModelSnapshotByOrderSnAndModelId(
                orderSn, modelId);
        return entity;
    }

    @Override
    public List<PlusProModelEntity> listOrderModelByOrderBySort(PlusOrderEntity orderEntity) {
        List<PlusProModelEntity> modelEntityList = null;
        if (PlusConstant.MERGE_CARD_LIST.contains(orderEntity.getConfigId())) {
            log.info("获取会员方案权益列表快照数据，会员单号 {}", orderEntity.getOrderSn());
            modelEntityList = converter.toPlusProModelEntityList(
                    orderProgramSnapRepository.listModelSnapshotByOrderSnBySort(
                            orderEntity.getOrderSn()));
            if (CollectionUtils.isNotEmpty(modelEntityList)){
                // 只有合并卡的开卡礼、月享红包、多买多送、拒就赔有发送节点，其他的都没有
                modelEntityList.stream()
                        .filter(item -> !PlusConstant.MODEL_SEND_CONTROL_LIST.contains(item.getModelId()))
                        .forEach(item -> item.setSendType(null));
            }
        }
        if (CollectionUtils.isEmpty(modelEntityList)) {
            log.info("获取会员方案权益列表实时数据，会员单号 {} 会员方案 {}", orderEntity.getOrderSn(),
                    orderEntity.getProgramId());
            modelEntityList = plusProModelRepository.getProModelByProgramIdOrderBySort(
                    orderEntity.getProgramId());
            // 无订单快照 - 保持原始发放方式逻辑
            if (CollectionUtils.isNotEmpty(modelEntityList)){
                modelEntityList.forEach(item -> item.setSendType(null));
            }
        }
        return modelEntityList;
    }

    @Override
    public Integer getOrderProgramSendNode(String orderSn, Integer programId) {
        PlusOrderProgramSnapshotEntity programSnapshot = orderProgramSnapRepository.getProgramSnapshotByOrderSn(
                orderSn);
        if (Objects.nonNull(programSnapshot)) {
            log.info("从会员订单快照获取发放节点 订单{} 发放节点{}", orderSn, programSnapshot.getSendNode());
            return programSnapshot.getSendNode();
        }
        if (Objects.nonNull(programId)) {
            PlusProgramEntity plusProgram = plusProgramRepository.getById(programId);
            if (Objects.nonNull(plusProgram)) {
                log.info("从会员方案获取发放节点 订单{} 发放节点{}", orderSn, plusProgram.getSendNode());
                return plusProgram.getSendNode();
            }
        }
        log.info("无法获取发放节点 订单{} programId{}", orderSn, programId);
        return null;
    }

}


