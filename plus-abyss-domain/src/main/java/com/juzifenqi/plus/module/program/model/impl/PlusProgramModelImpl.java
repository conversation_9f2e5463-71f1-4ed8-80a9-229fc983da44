package com.juzifenqi.plus.module.program.model.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.admin.program.CopyProgramReq;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramEditQueryReq;
import com.juzifenqi.plus.dto.resp.admin.program.ProgramQueryReq;
import com.juzifenqi.plus.enums.PlusLogBusinessTypeEnum;
import com.juzifenqi.plus.enums.PlusOptTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusProgramLogNodeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleCheckProfitDataEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.common.IPlusOptLogRepository;
import com.juzifenqi.plus.module.common.IPlusProgramLogRepository;
import com.juzifenqi.plus.module.common.IProductExternalRepository;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramQueryReq;
import com.juzifenqi.plus.module.program.model.contract.IPlusConfigRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusConfigEntity;
import com.juzifenqi.plus.module.program.model.IPlusProgramModel;
import com.juzifenqi.plus.module.program.model.contract.IPlusChannelManagerRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramRepository;
import com.juzifenqi.plus.module.program.model.contract.program.IPlusProgramEditPriceRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramEditPriceEntity;
import com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity;
import com.juzifenqi.plus.module.program.model.event.program.CopyProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.MultiplexChannelProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.CreateProgramEditPriceEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo;
import com.juzifenqi.plus.utils.RedisLock;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 方案model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 15:04
 */
@Slf4j
@Component
public class PlusProgramModelImpl implements IPlusProgramModel {

    @Autowired
    private IPlusProgramEditPriceRepository editPriceRepository;
    @Autowired
    private IPlusProgramRepository          programRepository;
    @Autowired
    private IPlusProgramLogRepository       logRepository;
    @Autowired
    private IPlusChannelManagerRepository   managerRepository;
    @Autowired
    private RedisLock                       redisLock;
    @Autowired
    private IPlusOptLogRepository           optLogRepository;
    @Autowired
    private IPlusProModelRepository         plusProModelRepository;
    @Autowired
    private ProfitHandlerContext            handlerContext;
    @Resource
    private IPlusProgramLogRepository       programLogRepository;
    @Resource
    private IPlusConfigRepository           plusConfigRepository;

    @Override
    public List<PlusProgramEditPriceEntity> getProgramEditPriceList(PlusProgramEditQueryReq req) {
        return editPriceRepository.getProgramEditPriceList(req);
    }

    @Override
    public Integer countProgramEditPrice(PlusProgramEditQueryReq req) {
        return editPriceRepository.countProgramEditPrice(req);
    }

    @Override
    public void addProgramEditPrice(CreateProgramEditPriceEvent event) {
        editPriceRepository.addProgramEditPrice(event);
    }

    @Override
    public void executeProgramEditPriceTask() {
        log.info("处理方案改价任务开始");
        List<PlusProgramEditPriceEntity> jobList = editPriceRepository.getJobList();
        if (CollectionUtils.isEmpty(jobList)) {
            log.info("方案改价任务列表为空");
            return;
        }
        for (PlusProgramEditPriceEntity entity : jobList) {
            if (entity == null) {
                continue;
            }
            boolean result = this.updateProgramPrice(entity.getProgramId(), entity.getPrice());
            editPriceRepository.updateProgramEditPrice(entity, result);
        }
        log.info("处理方案改价任务结束");
    }

    @Override
    public boolean updateProgramPrice(Integer programId, BigDecimal price) {
        return programRepository.updateProgramPrice(programId, price);
    }

    @Override
    public PageResultEntity<PlusProgramListEntity> getProgramList(ProgramQueryReq req) {
        return programRepository.getProgramList(req);
    }

    @Override
    public boolean existBackstageName(String backstageName, Integer programId, Integer channelId) {
        return programRepository.existBackstageName(backstageName, programId, channelId);
    }

    @Override
    public void addProgram(SavePlusProgramEvent event) {
        //兼容老逻辑，后付款不能为空
        if (!CollectionUtils.isEmpty(event.getPayTypes())) {
            if (event.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                event.setAfterPayState(1);
            } else {
                event.setAfterPayState(2);
            }
            if (!event.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                event.setFirstPayAmount(BigDecimal.ZERO);
            }
        }
        Integer id = programRepository.addProgram(event);
        String content = "新建会员方案--" + JSON.toJSONString(event);
        logRepository.saveLog(id, event.getOptUserId(), event.getOptUserName(),
                PlusProgramLogNodeEnum.LOG_NODE_CREATE_PROGRAM.getName(), content);
    }

    @Override
    public void editProgram(SavePlusProgramEvent event) {
        if (!event.getPayTypes().contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
            event.setFirstPayAmount(BigDecimal.ZERO);
        }
        programRepository.editProgram(event);
        logRepository.saveLog(event.getId(), event.getOptUserId(), event.getOptUserName(),
                PlusProgramLogNodeEnum.LOG_NODE_UPDATE_PROGRAM.getName(), JSON.toJSONString(event));
    }

    /**
     * 复用渠道方案
     */
    @Transactional
    @Override
    public void multiplexChannelProgram(MultiplexChannelProgramEvent event) {
        // 获取目标渠道+会员类型的配置
        List<PlusChannelFunctionPo> functionList = managerRepository.getFunctionListByChannelId(
                event.getTargetChannelId());
        if (CollectionUtils.isEmpty(functionList)) {
            throw new PlusAbyssException("目标渠道及方案未在渠道管理配置，请先配置再复用");
        }
        PlusChannelFunctionPo function = functionList.stream()
                .filter(e -> e.getConfigId().equals(event.getSourceConfigId())).findFirst()
                .orElse(null);
        if (function == null) {
            throw new PlusAbyssException("目标渠道及方案未在渠道管理配置，请先配置再复用");
        }
        String lockKey =
                RedisConstantPrefix.MULTIPLEX_PROGRAM_RESUBMIT_KEY + event.getTargetChannelId();
        try {
            // 防重提 100s一次
            if (!redisLock.lock(lockKey, "1", 100)) {
                throw new PlusAbyssException("请勿重复操作");
            }
            // 复用渠道方案信息
            multiplexChannelProgramInfo(function, event);
            // 添加操作日志
            optLogRepository.addOptLog(999999, PlusOptTypeEnum.MULTIPLEX_PROGRAM,
                    PlusLogBusinessTypeEnum.PROGRAM, event.getOptUser(),
                    String.valueOf(event.getOptUserId()),
                    PlusOptTypeEnum.MULTIPLEX_PROGRAM.getName());
        } catch (Exception e) {
            LogUtil.printLog(e, "复用渠道方案异常");
            if (e instanceof PlusAbyssException) {
                throw e;
            }
            throw new PlusAbyssException("复用渠道方案异常");
        } finally {
            redisLock.unLock(lockKey);
        }
    }

    @Override
    public ProgramDetailEntity getProgramDetail(Integer id) {
        ProgramDetailEntity detail = programRepository.getProgramDetail(id);
        if (detail != null) {
            List<PlusProModelEntity> list = plusProModelRepository.getPlusProModelList(id);
            detail.setList(list);
        }
        return detail;
    }

    /**
     * 复用渠道方案相关信息
     */
    private void multiplexChannelProgramInfo(PlusChannelFunctionPo function,
            MultiplexChannelProgramEvent event) {
        // 目标渠道+会员类型：权益
        List<String> plusProfitList = Arrays.asList(function.getPlusProfit().split(","));
        List<String> openModeList = Arrays.asList(function.getOpenMode().split(","));
        for (Integer sourceProgramId : event.getSourceProgramIds()) {
            CopyProgramEvent copyProgramEvent = new CopyProgramEvent();
            copyProgramEvent.setProgramId(sourceProgramId);
            copyProgramEvent.setCopyChannel(true);
            copyProgramEvent.setTargetChannelId(event.getTargetChannelId());
            copyProgramEvent.setPlusProfitList(plusProfitList);
            copyProgramEvent.setOpenModeList(openModeList);
            copyProgramEvent.setOptUserId(event.getOptUserId());
            copyProgramEvent.setOptUser(event.getOptUser());
            programRepository.copyProgram(copyProgramEvent);
        }
    }

    @Override
    public void programEffective(SavePlusProgramEvent event) {
        programRepository.programEffective(event);
        logRepository.saveLog(event.getId(), event.getOptUserId(), event.getOptUserName(),
                PlusProgramLogNodeEnum.LOG_NODE_TAKE_EFFECT.getName(),
                event.getOptUserName() + "生效了方案");
    }

    /**
     * 复制方案
     */
    @Transactional
    @Override
    public void copyProgram(CopyProgramReq req) {
        try {
            CopyProgramEvent copyProgramEvent = new CopyProgramEvent();
            copyProgramEvent.setProgramId(req.getId());
            copyProgramEvent.setCopyChannel(false);
            copyProgramEvent.setOptUserId(req.getOptUserId());
            copyProgramEvent.setOptUser(req.getOptUser());
            programRepository.copyProgram(copyProgramEvent);
        } catch (Exception e) {
            LogUtil.printLog(e, "复制方案异常");
            if (e instanceof PlusAbyssException) {
                throw e;
            }
            throw new PlusAbyssException("复制方案异常");
        }
    }

    @Override
    public List<PlusProgramEntity> getMemberPlusProgramAll(PlusProgramQueryReq req) {
        return programRepository.getMemberPlusProgramAll(req);
    }

    @Override
    public Boolean upAct(String id) {
        return programRepository.upAct(id);
    }

    @Override
    public Boolean downAct(String id) {
        return programRepository.downAct(id);
    }

    @Override
    public void saveMemberPlusProgramLog(Integer programId, Integer operatingId,
            String operatingName, String event, String content) {
        try {
            if (Objects.isNull(programId)) {
                programId = 0;
            }
            log.info("添加桔享Plus方案id:{}更新记录日志----start-------", programId);
            programLogRepository.saveLog(programId, operatingId, operatingName, event, content);
        } catch (Exception e) {
            log.error("保存桔享Plus方案更新记录日志信息出现异常", e);
        }
    }

    @Override
    public List<PlusProgramEntity> getUpProgramList() {
        return programRepository.getUpProgramList();
    }

    @Override
    public List<PlusConfigEntity> loadPlusConf() {
        return plusConfigRepository.loadPlusConf();
    }

}
