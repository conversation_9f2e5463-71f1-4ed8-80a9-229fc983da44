package com.juzifenqi.plus.module.order.api.converter;

import com.juzifenqi.plus.dto.req.CancelRenewPlanReq;
import com.juzifenqi.plus.dto.req.CancelRenewReq;
import com.juzifenqi.plus.dto.req.CreateOrderNoticeTaskReq;
import com.juzifenqi.plus.dto.req.PlusDeductForActiveReq;
import com.juzifenqi.plus.dto.req.PlusOrderCancelReq;
import com.juzifenqi.plus.dto.req.PlusOrderCreateReq;
import com.juzifenqi.plus.dto.req.PlusOrderRefundApplyReq;
import com.juzifenqi.plus.dto.req.PlusOrderRelationCreateReq;
import com.juzifenqi.plus.dto.req.PlusOrderRelationReq;
import com.juzifenqi.plus.dto.req.PlusPayCallbackReq;
import com.juzifenqi.plus.dto.req.PlusProductOrderCreateReq;
import com.juzifenqi.plus.dto.req.PlusRenewInfoReq;
import com.juzifenqi.plus.dto.req.PlusRenewalPlanInfoReq;
import com.juzifenqi.plus.dto.req.UpdOrderStateReq;
import com.juzifenqi.plus.dto.req.VirtualGoodsOrderCreateReq;
import com.juzifenqi.plus.dto.req.VirtualOrderCreateReq;
import com.juzifenqi.plus.dto.req.profits.ProductCheckReq;
import com.juzifenqi.plus.dto.req.profits.SendProfitsMqReq;
import com.juzifenqi.plus.dto.resp.PlusOrderCancelResp;
import com.juzifenqi.plus.dto.resp.PlusOrderCreateResp;
import com.juzifenqi.plus.dto.resp.PlusOrderDeductResp;
import com.juzifenqi.plus.dto.resp.PlusOrderPayInfoResp;
import com.juzifenqi.plus.dto.resp.PlusOrderRefundInfoResp;
import com.juzifenqi.plus.dto.resp.PlusOrderRelationResp;
import com.juzifenqi.plus.dto.resp.PlusProductOrderCreateResp;
import com.juzifenqi.plus.dto.resp.PlusRenewInfoResp;
import com.juzifenqi.plus.dto.resp.PlusRenewalPlanInfoResp;
import com.juzifenqi.plus.dto.resp.ProductCheckResultResp;
import com.juzifenqi.plus.dto.resp.VirtualCheckResultResp;
import com.juzifenqi.plus.dto.resp.VirtualGoodsOrderCreateResp;
import com.juzifenqi.plus.dto.resp.VirtualOrderCreateResp;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ProductCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.event.ProductCheckEvent;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderCancelAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderDeductAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderPayInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusPayCallbackAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewalPlanInfoAo;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.event.CancelRenewEvent;
import com.juzifenqi.plus.module.order.model.event.CancelRenewalPlanEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRenewInfoEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRenewalPlanInfoEvent;
import com.juzifenqi.plus.module.order.model.event.notice.PlusOrderNoticeEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderMessageMqOutEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayCallbackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRefundApplyEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusProductOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.UpdOrderStateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualGoodsOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualOrderCreateEvent;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface IOrderApiConverter {

    IOrderApiConverter instance = Mappers.getMapper(IOrderApiConverter.class);

    PlusOrderCreateEvent toPlusOrderCreateEvent(PlusOrderCreateReq plusOrderCreateReq);

    VirtualOrderCreateEvent toVirtualOrderCreateEvent(VirtualOrderCreateReq virtualOrderCreateReq);

    VirtualGoodsOrderCreateEvent toVirtualGoodsOrderCreateEvent(VirtualGoodsOrderCreateReq req);

    PlusProductOrderCreateEvent toPlusProductOrderCreateEvent(PlusProductOrderCreateReq req);

    PlusOrderCreateResp toPlusOrderCreateResp(PlusOrderAo plusOrderAO);

    VirtualOrderCreateResp toVirtualOrderCreateResp(PlusOrderAo plusOrderAO);

    VirtualGoodsOrderCreateResp toVirtualGoodsOrderCreateResp(PlusOrderAo plusOrderAO);

    PlusProductOrderCreateResp toPlusProductOrderCreateResp(PlusOrderAo plusOrderAO);

    PlusOrderCancelEvent toPlusOrderCancelEvent(PlusOrderCancelReq plusOrderCancelReq);

    PlusOrderCancelResp toPlusOrderCancelResp(PlusOrderCancelAo plusOrderAo);

    PlusDeductEvent toDeductEvent(PlusDeductForActiveReq plusOrderDeductReq);

    PlusOrderDeductResp toPlusOrderDeductResp(PlusOrderDeductAo plusOrderDeductAo);

    PlusOrderPayCallbackEvent toPlusOrderPayCallbackEvent(PlusPayCallbackReq plusPayCallbackReq);


    @Mappings({
            @Mapping(target = "relationBusinessType", expression = "java(com.juzifenqi.plus.enums.PlusOrderRelationBusinessType.getByCode(req.getBusinessType()))")})
    PlusOrderRelationCreateEvent toPlusOrderRelationCreateEvent(PlusOrderRelationCreateReq req);

    PlusRenewInfoEvent toPlusOrderRenewInfoEvent(PlusRenewInfoReq req);

    PlusRenewInfoResp toPlusRenewInfoResp(PlusRenewInfoAo renewInfoAo);

    CancelRenewEvent toCancelRenewInfoResp(CancelRenewReq cancelRenewReq);

    PlusOrderRelationEvent toPlusOrderRelationEvent(PlusOrderRelationReq req);

    List<PlusOrderRelationResp> toPlusOrderRelationResp(List<PlusOrderRelationEntity> list);

    VirtualCheckResultResp toVirtualCheckResultResp(VirtualCheckResultEntity entity);

    PlusOrderRefundApplyEvent toPlusOrderRefundApplyEvent(PlusOrderRefundApplyReq req);

    PlusOrderNoticeEvent toPlusOrderNoticeEvent(CreateOrderNoticeTaskReq req);

    @Mappings({@Mapping(target = "plusOrderSn", source = "ao.orderSn"),
            @Mapping(target = "userId", source = "req.userId"),
            @Mapping(target = "channelId", source = "req.channelId"),
            @Mapping(target = "programId", source = "req.programId"),
            @Mapping(target = "orderSn", source = "req.createOrderContext.loanOrderSn"),
            @Mapping(target = "configId", source = "ao.configId")})
    SendProfitsMqReq toSendProfitsMqReq(PlusOrderCreateReq req, PlusOrderAo ao);

    ProductCheckEvent toProductCheckEvent(ProductCheckReq req);

    ProductCheckResultResp toProductCheckResultResp(ProductCheckResultEntity entity);

    UpdOrderStateEvent toUpdOrderStateEvent(UpdOrderStateReq req);

    @Mappings({@Mapping(target = "userId", source = "req.userId"),
            @Mapping(target = "channelId", source = "req.channelId"),
            @Mapping(target = "programId", source = "req.programId"),
            @Mapping(target = "configId", source = "ao.configId"),
            @Mapping(target = "orderSn", source = "ao.orderSn"),
            @Mapping(target = "notifyType", source = "notifyType")})
    PlusOrderMessageMqOutEvent toPlusOrderMessageMqOutEvent(Integer notifyType, PlusOrderAo ao,
            PlusOrderCreateReq req);

    @Mappings({@Mapping(target = "plusOrderSn", source = "event.orderSn"),
            @Mapping(target = "userId", source = "event.memberId"),
            @Mapping(target = "channelId", source = "ao.channelId"),
            @Mapping(target = "programId", source = "ao.programId"),
            @Mapping(target = "orderSn", source = "event.loanOrderSn"),
            @Mapping(target = "configId", source = "ao.configId")})
    SendProfitsMqReq toSendProfitsMqReq(PlusOrderPayCallbackEvent event, PlusPayCallbackAo ao);

    @Mappings({@Mapping(target = "userId", source = "event.memberId"),
            @Mapping(target = "channelId", source = "ao.channelId"),
            @Mapping(target = "programId", source = "ao.programId"),
            @Mapping(target = "configId", source = "ao.configId"),
            @Mapping(target = "orderSn", source = "event.orderSn"),
            @Mapping(target = "notifyType", source = "notifyType")})
    PlusOrderMessageMqOutEvent toPlusOrderMessageMqOutEvent(Integer notifyType,
            PlusOrderPayCallbackEvent event, PlusPayCallbackAo ao);

    PlusOrderPayInfoResp toPlusOrderPayInfoResp(PlusOrderPayInfoAo plusOrderPayInfoAo);

    @Mappings({@Mapping(target = "dealState", source = "refundState")})
    PlusOrderRefundInfoResp toPlusOrderRefundInfoResp(PlusOrderRefundInfoEntity plusOrderRefundInfoEntity);

    PlusRenewalPlanInfoEvent toPlusOrderRenewalPlanInfoEvent(PlusRenewalPlanInfoReq plusRenewPlanInfoReq);

    CancelRenewalPlanEvent toCancelRenewalPlanEvent(CancelRenewPlanReq cancelRenewPlanReq);

    PlusRenewalPlanInfoResp toPlusRenewalPlanInfoResp(PlusRenewalPlanInfoAo renewInfo);
}
