package com.juzifenqi.plus.module.market.application.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.groot.utils.core.date.LocalDateTimeUtils;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.magic.bean.enums.MarketStateEnum;
import com.juzifenqi.magic.bean.enums.SceneCodeEnum;
import com.juzifenqi.magic.bean.vo.PlusMarketDetailVO;
import com.juzifenqi.magic.bean.vo.PlusMarketDetailVO.ProgramBasicInfo;
import com.juzifenqi.order.dao.entity.Orders;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.EngineCodeConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.enums.market.ShowStatusEnum;
import com.juzifenqi.plus.enums.market.ShowTypeEnum;
import com.juzifenqi.plus.module.common.IEngineExternalRepository;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.common.IMagicExternalRepository;
import com.juzifenqi.plus.module.common.entity.DistributionMarketEntity;
import com.juzifenqi.plus.module.common.entity.PolicyStyleConfigEntity;
import com.juzifenqi.plus.module.common.event.MarketEvent;
import com.juzifenqi.plus.module.market.application.IPlusMarketApplication;
import com.juzifenqi.plus.module.market.application.converter.IPlusMarketApplicationConverter;
import com.juzifenqi.plus.module.market.model.IPlusMarketModel;
import com.juzifenqi.plus.module.market.model.PlusLoanDiscountInfoEntity;
import com.juzifenqi.plus.module.market.model.contract.entity.*;
import com.juzifenqi.plus.module.market.model.event.PlusBillListMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLeadPageMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLoanConfirmMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLoanConfirmRdzxMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusLoanMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusOrderListMarketEvent;
import com.juzifenqi.plus.module.market.model.event.PlusPaySuccessMarketEvent;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderRdzxApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderShuntApplication;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDiscountEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusShuntResultEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;
import com.juzifenqi.plus.utils.PlusConcatUtils;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzishuke.credit.vo.CreditCalculateVO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 会员营销
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/22 18:21
 */
@Slf4j
@Service
public class IPlusMarketApplicationImpl implements IPlusMarketApplication {

    private final IPlusMarketApplicationConverter converter = IPlusMarketApplicationConverter.instance;

    @Autowired
    private IPlusOrderShuntApplication shuntApplication;
    @Autowired
    private IPlusOrderRdzxApplication  rdzxApplication;
    @Autowired
    private IPlusOrderApplication      orderApplication;
    @Autowired
    private IMagicExternalRepository   magicExternalRepository;
    @Autowired
    private ConfigProperties           configProperties;
    @Autowired
    private IPlusProgramQueryModel     programQueryModel;
    @Autowired
    private IEngineExternalRepository  engineExternalRepository;
    @Autowired
    private RedisUtils                 redisUtils;
    @Autowired
    private IOrderExternalRepository   orderExternalRepository;
    @Autowired
    private IPlusMarketModel           marketModel;
    @Autowired
    private IIMRepository              iimRepository;

    @Override
    public PlusPaySuccessMarketEntity paySuccessMarket(PlusPaySuccessMarketEvent event) {
        try {
            log.info("信用支付成功页支付营销入参：{}", JSON.toJSONString(event));
            MarketEvent marketEvent = converter.toMarketEvent(event, SceneCodeEnum.PAY_SUCCESS);
            PlusMarketDetailVO marketDetail = magicExternalRepository.marketForScene(marketEvent);
            if (marketDetail == null) {
                log.info("信用支付成功页支付营销，营销服务调用失败：{}", event.getUserId());
                return new PlusPaySuccessMarketEntity().setNoMarketReason("服务调用失败");
            }
            // 不营销
            if (marketDetail.getMarketState() == MarketStateEnum.NO_MARKET.getCode()) {
                log.info("信用支付成功页支付营销，营销服务返回不营销：{}", event.getUserId());
                return new PlusPaySuccessMarketEntity().setNoMarketReason(marketDetail.getNoMarketReason());
            }
            List<ProgramBasicInfo> marketList = marketDetail.getMarketList();
            if (CollectionUtils.isEmpty(marketList)) {
                log.info("信用支付成功页支付营销，营销服务未返回可营销列表：{}", event.getUserId());
                return new PlusPaySuccessMarketEntity().setNoMarketReason(marketDetail.getNoMarketReason());
            }
            ProgramBasicInfo programInfo = marketList.get(0);
            Integer configId = programInfo.getConfigId();
            // 获取611或501状态借款单
            Orders orders = orderExternalRepository.lastOneProcessOrder(event.getUserId());
            // 桔策营销样式
            DistributionMarketEntity marketResult = engineExternalRepository.getDistributionMarketResult(
                    orders != null ? orders.getOrderSn() : null, event.getUserId(),
                    EngineCodeConstant.VIP_DOWN, event.getChannelId());
            String styleCode = getStyleCode(marketResult, SceneCodeEnum.PAY_SUCCESS.getCode(),
                    configId, event.getUserId());
            PlusPaySuccessMarketEntity entity = converter.toPlusPaySuccessMarketEntity(programInfo,
                    ShowTypeEnum.getShowTypeByConfigId(programInfo.getConfigId()), styleCode);
            // 分流
            PlusShuntResultEntity shuntResult = shuntApplication.shuntSupplier(
                    converter.toShuntEvent(event, entity.getProgramId(),
                            programInfo.getConfigId()));
            entity.setShuntSupplierId(shuntResult.getSupplierId());
            entity.setContractList(shuntResult.getContractList());
            entity.setSupplierName(shuntResult.getSupplierName());

            // 填充权益列表数据
            List<PlusModelEntity> plusRightsList = Lists.newArrayList();
            List<PlusProModelEntity> plusModelList = marketModel.getPlusModelList(programInfo.getProgramId());
            if (!CollectionUtils.isEmpty(plusModelList)){
                plusModelList.forEach(o->{
                    PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(o.getModelId());
                    if (!Objects.isNull(plusModelEnum)){
                        plusRightsList.add(new PlusModelEntity(plusModelEnum.getModelName(),plusModelEnum.getShortPY()));
                    }
                });
            }
            entity.setPlusRightsList(plusRightsList);

            // 是否支持后付款开通标识
            /*boolean b = programQueryModel.supportAfterPay(programInfo.getProgramId(), event.getUserId(),
                    event.getChannelId());
            entity.setAfterPayState(b ? 1 : 2);*/
            PlusProgramEntity programEntity = programQueryModel.supportPayTypes(programInfo.getProgramId(), event.getUserId(),
                    event.getChannelId());
            entity.setPayTypes(programEntity.getPayTypes());
            entity.setFirstPayAmount(programEntity.getFirstPayAmount());
            if (!CollectionUtils.isEmpty(programEntity.getPayTypes())) {
                List<Integer> payTypes = programEntity.getPayTypes();
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                    entity.setAfterPayState(1);
                } else {
                    entity.setAfterPayState(2);
                }
            }
            return entity;
        } catch (Exception e) {
            LogUtil.printLog(e, "信用支付完成页营销异常");
            iimRepository.sendImMessage("营销支付完成页营销异常，用户id：" + event.getUserId());
        }
        // 异常默认不营销
        return new PlusPaySuccessMarketEntity().setNoMarketReason("服务异常");
    }

    @Override
    public PlusOrderListMarketEntity orderListMarket(PlusOrderListMarketEvent event) {
        try {
            log.info("订单列表页-加速卡营销入参：{}", JSON.toJSONString(event));
            MarketEvent marketEvent = converter.toMarketEvent(event, SceneCodeEnum.ORDER_LIST);
            PlusMarketDetailVO marketDetail = magicExternalRepository.marketForScene(marketEvent);
            if (marketDetail == null) {
                log.info("订单列表页-加速卡营销，营销服务调用失败：{}", event.getUserId());
                return new PlusOrderListMarketEntity().setNoMarketReason("服务调用异常");
            }
            // 不营销
            if (marketDetail.getMarketState() == MarketStateEnum.NO_MARKET.getCode()) {
                log.info("订单列表页-加速卡营销，营销服务返回不营销：{}", event.getUserId());
                return new PlusOrderListMarketEntity().setNoMarketReason(marketDetail.getNoMarketReason());
            }
            List<ProgramBasicInfo> marketList = marketDetail.getMarketList();
            if (CollectionUtils.isEmpty(marketList)) {
                log.info("订单列表页-加速卡营销，营销服务未返回可营销列表：{}", event.getUserId());
                return new PlusOrderListMarketEntity().setNoMarketReason(marketDetail.getNoMarketReason());
            }
            ProgramBasicInfo programInfo = marketList.get(0);
            // 落地页地址拼接
            log.info("加速卡营销数据配置中心返回:{}", configProperties.suMarketingInfo);
            PlusOrderListMarketEntity confInfo = JSONObject.parseObject(
                    configProperties.suMarketingInfo, PlusOrderListMarketEntity.class);
            if (confInfo == null || confInfo.getLandingPageUrl() == null) {
                return new PlusOrderListMarketEntity();
            }
            Integer configId = programInfo.getConfigId();
            // 获取611或501状态借款单
            Orders orders = orderExternalRepository.lastOneProcessOrder(event.getUserId());
            // 桔策营销样式
            DistributionMarketEntity marketResult = engineExternalRepository.getDistributionMarketResult(
                    orders != null ? orders.getOrderSn() : null, event.getUserId(),
                    EngineCodeConstant.VIP_DOWN, event.getChannelId());
            String styleCode = getStyleCode(marketResult, SceneCodeEnum.ORDER_LIST.getCode(),
                    configId, event.getUserId());
            String landingPageUrl = confInfo.getLandingPageUrl() + programInfo.getProgramId();
            PlusOrderListMarketEntity entity = converter.toPlusOrderListMarketEntity(programInfo,
                    ShowStatusEnum.SHOW.getCode(), landingPageUrl, confInfo.getEntranceImg(),
                    styleCode);
            // 加速卡需要分流
            log.info("订单列表页-加速卡,分流开始：{}", event.getUserId());
            PlusShuntResultEntity shuntResult = shuntApplication.shuntSupplier(
                    converter.toShuntEvent(event, entity.getProgramId(), entity.getConfigId()));
            entity.setShuntSupplierId(shuntResult.getSupplierId());
            entity.setContractList(shuntResult.getContractList());
            entity.setSupplierName(shuntResult.getSupplierName());

            entity.setProgramName(programInfo.getName());
            entity.setConfigId(programInfo.getConfigId());
            entity.setRaiseAmount(programInfo.getRaiseAmount());
            // 填充权益列表数据
            List<PlusModelEntity> plusRightsList = Lists.newArrayList();
            List<PlusProModelEntity> plusModelList = marketModel.getPlusModelList(programInfo.getConfigId());
            if (!CollectionUtils.isEmpty(plusModelList)){
                plusModelList.forEach(o->{
                    PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(o.getModelId());
                    if (!Objects.isNull(plusModelEnum)){
                        plusRightsList.add(new PlusModelEntity(plusModelEnum.getModelName(),plusModelEnum.getShortPY()));
                    }
                });
            }
            entity.setPlusRightsList(plusRightsList);

            //是否支持后付款开通标识
            /*boolean b = programQueryModel.supportAfterPay(programInfo.getProgramId(), event.getUserId(),
                    event.getChannelId());
            entity.setAfterPayState(b ? 1 : 2);*/
            entity.setMallMobilePrice(programInfo.getMallMobilePrice());
            PlusProgramEntity programEntity = programQueryModel.supportPayTypes(programInfo.getProgramId(), event.getUserId(),
                    event.getChannelId());
            entity.setPayTypes(programEntity.getPayTypes());
            entity.setFirstPayAmount(programEntity.getFirstPayAmount());
            if (!CollectionUtils.isEmpty(programEntity.getPayTypes())) {
                List<Integer> payTypes = programEntity.getPayTypes();
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                    entity.setAfterPayState(1);
                } else {
                    entity.setAfterPayState(2);
                }
            }
            return entity;
        } catch (Exception e) {
            LogUtil.printLog(e, "订单列表页加速营销异常");
            iimRepository.sendImMessage("订单列表页加速营销异常，用户id：" + event.getUserId());
        }
        return new PlusOrderListMarketEntity().setNoMarketReason("服务异常");
    }

    @Override
    public PlusBillListMarketEntity billListMarket(PlusBillListMarketEvent event) {
        try {
            log.info("账单列表页-还款卡营销入参：{}", JSON.toJSONString(event));
            MarketEvent marketEvent = converter.toMarketEvent(event, SceneCodeEnum.BILL);
            PlusMarketDetailVO marketDetail = magicExternalRepository.marketForScene(marketEvent);
            if (marketDetail == null) {
                return new PlusBillListMarketEntity("调用营销失败,请重试");
            }
            // 不营销
            if (marketDetail.getMarketState() == MarketStateEnum.NO_MARKET.getCode()) {
                return new PlusBillListMarketEntity(
                        StringUtils.isBlank(marketDetail.getNoMarketReason()) ? "不营销"
                                : marketDetail.getNoMarketReason());
            }
            List<ProgramBasicInfo> marketList = marketDetail.getMarketList();
            if (CollectionUtils.isEmpty(marketList)) {
                return new PlusBillListMarketEntity("未返回营销列表");
            }
            ProgramBasicInfo programInfo = marketList.get(0);
            Integer programId = programInfo.getProgramId();
            PlusProgramEntity program = programQueryModel.getById(programId);
            if (program == null) {
                return new PlusBillListMarketEntity("营销方案无效");
            }
            // 获取方案续费配置
            PlusRenewRelevancePo renewRelevance = programQueryModel.getRenewRelevanceByProgramId(
                    programId);
            if (Objects.isNull(renewRelevance)) {
                return new PlusBillListMarketEntity("未获取到续费信息");
            }
            Integer isRenew = renewRelevance.getIsRenew();
            BigDecimal renewPrice = Objects.isNull(renewRelevance.getRenewPrice()) ? BigDecimal.ZERO
                    : renewRelevance.getRenewPrice().setScale(2);
            PlusBillListMarketEntity result = converter.toPlusBillListMarketEntity(program, isRenew,
                    renewPrice);

            // 20231101 zjf 还款卡AB测样式、还款券总额
            result.setPageStyle(
                    engineExternalRepository.getPageStyle(event.getUserId(), event.getChannelId()));
            result.setRepayCouponAmount(marketModel.getRepayCouponAmount(program.getProgramId()));
            result.setMarket(true);

            //首付支付
            PlusProgramEntity programEntity = programQueryModel.supportPayTypes(programId, event.getUserId(),
                    event.getChannelId());
            result.setPayTypes(programEntity.getPayTypes());
            result.setFirstPayAmount(programEntity.getFirstPayAmount());
            if (!CollectionUtils.isEmpty(programEntity.getPayTypes())) {
                List<Integer> payTypes = programEntity.getPayTypes();
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                    result.setAfterPayState(1);
                } else {
                    result.setAfterPayState(2);
                }
            }

            return result;
        } catch (Exception e) {
            LogUtil.printLog(e, "账单列表页还款卡营销异常");
            iimRepository.sendImMessage("账单列表页还款卡营销异常，用户id：" + event.getUserId());
        }
        return new PlusBillListMarketEntity("营销失败,请重试");
    }

    @Override
    public PlusLoanConfirmRdzxMarketEntity loanConfirmRdzxMarket(
            PlusLoanConfirmRdzxMarketEvent event) {
        log.info("确认借款页-融担卡营销入参：{}", JSON.toJSONString(event));
        Integer userId = event.getUserId();
        try {
            String redisKey = RedisConstantPrefix.PLUS_CAN_BUY + userId + "_"
                    + JuziPlusEnum.RDZX_CARD.getCode() + "_" + event.getLoanAmount();
            if (redisUtils.hasKey(redisKey)) {
                //从缓存中获取上次校验结果
                String marketInfo = redisUtils.get(redisKey);
                log.info("缓存中获取融担咨询卡是否营销,userId:{},marketInfo:{}", userId,
                        marketInfo);
                return JSONObject.parseObject(marketInfo, PlusLoanConfirmRdzxMarketEntity.class);
            }
            PlusLoanConfirmRdzxMarketEntity result = getRdzxMarketResult(event);
            log.info("获取融担咨询卡营销信息：{},{}", userId, JSONObject.toJSONString(result));
            // 不营销缓存10s
            if (result.getAllMarketState() == 0) {
                redisUtils.setEx(redisKey, JSONObject.toJSONString(result), 10, TimeUnit.SECONDS);
                return result;
            }
            // 营销缓存30s
            redisUtils.setEx(redisKey, JSONObject.toJSONString(result), 30, TimeUnit.SECONDS);
            return result;
        } catch (Exception e) {
            LogUtil.printLog(e, "确认借款页融担卡营销异常");
            iimRepository.sendImMessage("确认借款页融担卡营销异常，用户id：" + event.getUserId());
        }
        return new PlusLoanConfirmRdzxMarketEntity();
    }

    @Override
    public PlusLeadPageMarketEntity leadPageMarket(PlusLeadPageMarketEvent event) {
        log.info("导流页-桔省卡营销入参：{}", JSON.toJSONString(event));
        PlusLeadPageMarketEntity result = new PlusLeadPageMarketEntity();
        result.setShowType(0);
        Integer userId = event.getUserId();
        Integer channelId = event.getChannelId();
        try {
            String redisKey = PlusConcatUtils.symbolBelowStr(
                    RedisConstantPrefix.LEAD_PAGE_NO_TARGET, userId, channelId);
            String leadInfo = redisUtils.get(redisKey);
            if (StringUtils.isNotBlank(leadInfo)) {
                log.info("缓存获取导流会员营销信息redis返回 key:{},val:{}", redisKey, leadInfo);
                return JSONObject.parseObject(leadInfo, PlusLeadPageMarketEntity.class);
            }
            ProgramBasicInfo leadMarketResult = getLeadMarketResult(event);
            if (leadMarketResult == null) {
                redisUtils.setEx(redisKey, JSONObject.toJSONString(result), 20, TimeUnit.SECONDS);
                return result;
            }
            // 设置方案id
            result.setProgramId(leadMarketResult.getProgramId());
            // 设置方案价格
            result.setMallMobilePrice(leadMarketResult.getMallMobilePrice());
            result.setShowType(leadMarketResult.getConfigId());
            // 正常路由，存入短时间缓存 如果不路由任何卡20s 路由3s
            redisUtils.setEx(redisKey, JSONObject.toJSONString(result), 3, TimeUnit.SECONDS);
        } catch (Exception e) {
            LogUtil.printLog(e, "导流页桔省卡营销异常");
            iimRepository.sendImMessage("导流页桔省卡营销异常，用户id：" + event.getUserId());
        }
        return result;
    }

    @Override
    public PlusLoanConfirmMarketEntity loanConfirmMarket(PlusLoanConfirmMarketEvent event) {
        PlusLoanConfirmMarketEntity result = new PlusLoanConfirmMarketEntity();
        try {
            log.info("确认借款页营销入参：{}", JSON.toJSONString(event));
            MarketEvent marketEvent = converter.toMarketEvent(event, SceneCodeEnum.CONFIRM_LOAN);
            PlusMarketDetailVO marketDetail = magicExternalRepository.marketForScene(marketEvent);
            if (marketDetail == null) {
                log.info("确认借款页营销，营销服务调用失败：{}", event.getUserId());
                return result.setNoMarketReason("服务调用异常");
            }
            PlusLoanConfirmInfoEntity entity = marketModel.handleLoanConfirmInfo(event);
            result.setBuyQuotaFlag(entity.isSevenQuotaPlusOrder() ? 1 : 0);
            if (entity.getWaitPayOrder() != null) {
                // 校验是否能有条件取消
                try {
                    PlusOrderEntity order = entity.getWaitPayOrder();
                    PlusOrderCancelEvent cancelEvent = converter.toPlusOrderCancelEvent(order);
                    orderApplication.cancelPreCheckRule(PlusCancelTypeEnum.CONDITION, cancelEvent);
                    result.setAfterPayOrder(order.getOrderSn());
                    result.setAfterPayProgramId(order.getProgramId());
                } catch (Exception e) {
                    LogUtil.printLog(e, "确认借款页校验是否能取消后付款待支付订单异常");
                }
            }
            // 不营销
            if (marketDetail.getMarketState() == MarketStateEnum.NO_MARKET.getCode()) {
                log.info("确认借款页营销，营销服务返回不营销：{}", event.getUserId());
                return result.setNoMarketReason(marketDetail.getNoMarketReason());
            }
            List<ProgramBasicInfo> marketList = marketDetail.getMarketList();
            if (CollectionUtils.isEmpty(marketList)) {
                log.info("确认借款页营销，营销服务未返回可营销列表：{}", event.getUserId());
                return result.setNoMarketReason(marketDetail.getNoMarketReason());
            }
            ProgramBasicInfo program = marketList.get(0);
            Integer configId = program.getConfigId();
            Integer programId = program.getProgramId();
            // 是否支持后付款开通标识
            /*boolean b = programQueryModel.supportAfterPay(programId, event.getUserId(),
                    event.getChannelId());
            result.setAfterPayState(b ? 1 : 2);*/
            // 获取611或501状态借款单
            Orders orders = orderExternalRepository.lastOneProcessOrder(event.getUserId());
            // 桔策营销样式
            DistributionMarketEntity distributionMarketResult = engineExternalRepository.getDistributionMarketResult(
                    orders != null ? orders.getOrderSn() : null, event.getUserId(),
                    EngineCodeConstant.VIP_DOWN, event.getChannelId());
            int quotaStyle = 3;
            String styleCode = null;
            try {
                styleCode = getStyleCode(distributionMarketResult,
                        SceneCodeEnum.CONFIRM_LOAN.getCode(), configId, event.getUserId());
                quotaStyle = StringUtils.isNotBlank(styleCode) ? Integer.parseInt(styleCode) : 3;
            } catch (Exception e) {
                LogUtil.printLog(e, "确认借款页获取提额卡样式异常");
            }
            result.setQuotaStyle(quotaStyle);
            result.setStyleCode(styleCode);
            result.setQuotaPopup(distributionMarketResult.getQuotaPopup());
            result.setAdvertisement(distributionMarketResult.getAdvertisement());
            // 延迟划扣时间
            result.setDelayTime(marketModel.getDelayDeductTime(configId));
            // 小额月卡【结清返现】权益【返现金额】
            if (configId == JuziPlusEnum.XEYK_CARD.getCode()) {
                result.setSettleCashbackAmount(
                        programQueryModel.getSettleCashbackAmount(configId, programId));
            }
            result.setShowContent(marketModel.getShowContent(programId, configId, "确认借款页"));
            result.setRaiseAmount(program.getRaiseAmount());
            result.setMallMobilePrice(program.getMallMobilePrice());
            // 提额后可借款金额
            CreditCalculateVO calculateVO = marketModel.creditCalculateSimulation(event.getUserId(),
                    programId, result.getRaiseAmount());
            result.setVipCanWithdralAmount(
                    calculateVO != null ? calculateVO.getBorrowUsableSumAll() : null);
            // 折扣信息
            PlusDiscountEntity discount = marketModel.getDiscount(event.getUserId(),
                    event.getChannelId(), configId, event.getSceneCode(), distributionMarketResult);
            if (discount != null && discount.getShowDiscountInfo() == 1) {
                result.setShowDiscountInfo(discount.getShowDiscountInfo());
                result.setDiscountRate(discount.getDiscountRate());
                result.setDiscountEndTime(
                        discount.getDiscountEndTime().getTime() - new Date().getTime());
                result.setDiscountPrice(
                        program.getMallMobilePrice().multiply(discount.getDiscountRate())
                                .setScale(0, RoundingMode.DOWN));
            }
            // 分流
            log.info("确认借款页营销分流开始：{}", event.getUserId());
            PlusShuntResultEntity shuntResult = shuntApplication.shuntSupplier(
                    converter.toShuntEvent(event, programId, configId,
                            result.getDiscountPrice() != null ? result.getDiscountPrice()
                                    : program.getMallMobilePrice()));
            result.setShuntSupplierId(shuntResult.getSupplierId());
            result.setContractList(shuntResult.getContractList());
            result.setSupplierName(shuntResult.getSupplierName());
            result.setProgramId(programId);
            result.setConfigId(configId);
            result.setProgramName(program.getName());

            // 填充权益列表数据
            List<PlusModelEntity> plusRightsList = Lists.newArrayList();
            List<PlusProModelEntity> plusModelList = marketModel.getPlusModelList(programId);
            if (!CollectionUtils.isEmpty(plusModelList)){
                plusModelList.forEach(o->{
                    PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(o.getModelId());
                    if (!Objects.isNull(plusModelEnum)){
                        plusRightsList.add(new PlusModelEntity(plusModelEnum.getModelName(),plusModelEnum.getShortPY()));
                    }
                });
            }
            result.setPlusRightsList(plusRightsList);
            // 设置营销卡类型
            result.setShowType(ShowTypeEnum.getShowTypeByConfigId(configId));

            //首付支付设置
            PlusProgramEntity programEntity = programQueryModel.supportPayTypes(programId, event.getUserId(),
                    event.getChannelId());
            result.setPayTypes(programEntity.getPayTypes());
            result.setFirstPayAmount(programEntity.getFirstPayAmount());
            if (!CollectionUtils.isEmpty(programEntity.getPayTypes())) {
                List<Integer> payTypes = programEntity.getPayTypes();
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                    result.setAfterPayState(1);
                } else {
                    result.setAfterPayState(2);
                }
                //如果支持首期支付计算首付金额
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                    if (discount != null && discount.getShowDiscountInfo() == 1) {
                        result.setFirstPayAmount(
                                programEntity.getFirstPayAmount().multiply(discount.getDiscountRate())
                                        .setScale(0, RoundingMode.DOWN));
                    }
                }
            }

        } catch (Exception e) {
            log.info("确认借款页营销异常", e);
            iimRepository.sendImMessage("确认借款页营销异常，用户id：" + event.getUserId());
        }
        return result;
    }

    /**
     * 获取营销策略样式配置
     */
    private String getStyleCode(DistributionMarketEntity marketResult, Integer sceneCode,
            Integer configCode, Integer userId) {
        Map<String, List<PolicyStyleConfigEntity>> policyStyleConfigMap = marketResult.getPolicyStyleConfigs();
        if (policyStyleConfigMap == null) {
            log.info("上下文获取营销策略样式配置map为空：{}", userId);
            return null;
        }
        List<PolicyStyleConfigEntity> policyStyleConfigList = policyStyleConfigMap.get(
                String.valueOf(sceneCode));
        if (CollectionUtils.isEmpty(policyStyleConfigList)) {
            log.info("上下文获取营销策略样式配置list为空：{}", userId);
            return null;
        }
        PolicyStyleConfigEntity config = policyStyleConfigList.stream()
                .filter(e -> Objects.equals(e.getConfigCode(), configCode)).findFirst()
                .orElse(null);
        if (config == null) {
            log.info("上下文获取营销策略样式配置为空：{}", userId);
            return null;
        }
        return config.getStyleCode();
    }

    @Override
    public PlusLoanMarketEntity loanMarket(PlusLoanMarketEvent event) {
        PlusLoanMarketEntity result = new PlusLoanMarketEntity();
        try {
            Integer userId = event.getUserId();
            // 用户id尾号单双数
            result.setTailNumber(userId % 2);
            log.info("借款首页营销入参：{}", JSON.toJSONString(event));
            MarketEvent marketEvent = converter.toMarketEvent(event, SceneCodeEnum.LOAN);
            PlusMarketDetailVO marketDetail = magicExternalRepository.marketForScene(marketEvent);
            if (marketDetail == null) {
                log.info("借款首页营销，营销服务调用失败：{}", event.getUserId());
                return result.setNoMarketReason("服务调用失败");
            }
            // 获取611或501状态借款单
            Orders orders = orderExternalRepository.lastOneProcessOrder(event.getUserId());
            // 桔策营销样式
            DistributionMarketEntity disResult = engineExternalRepository.getDistributionMarketResult(
                    orders != null ? orders.getOrderSn() : null, event.getUserId(),
                    EngineCodeConstant.VIP_DOWN, event.getChannelId());
            // 7天内买过卡的重提客群、重提客群二次弹窗处理
            PlusLoanInfoEntity loanInfo = marketModel.handleLoanInfo(event, disResult, orders);
            result.setResubmitCustomerGroupFlag(loanInfo.getResubmitCustomerGroupFlag());
            result.setReduceQuotaFlag(loanInfo.getReduceQuotaFlag());
            result.setWaiveLoanFlag(loanInfo.getWaiveLoanFlag());
            result.setOrderSn(orders != null ? orders.getOrderSn() : null);
            result.setResubmitGroupOrderAmount(loanInfo.getResubmitGroupOrderAmount());
            result.setResubmitGroupOrderPeriods(loanInfo.getResubmitGroupOrderPeriods());
            result.setCapitalId(loanInfo.getCapitalId());
            result.setSevenOpenOrderAmount(loanInfo.getSevenOpenOrderAmount());
            // 不营销
            if (marketDetail.getMarketState() == MarketStateEnum.NO_MARKET.getCode()) {
                log.info("借款首页营销，营销服务返回不营销：{}", event.getUserId());
                return result.setNoMarketReason(marketDetail.getNoMarketReason());
            }
            List<ProgramBasicInfo> marketList = marketDetail.getMarketList();
            if (CollectionUtils.isEmpty(marketList)) {
                log.info("借款首页营销，营销服务未返回可营销列表：{}", event.getUserId());
                return result.setNoMarketReason(marketDetail.getNoMarketReason());
            }
            ProgramBasicInfo program = marketList.get(0);
            Integer configId = program.getConfigId();
            Integer programId = program.getProgramId();
            result.setDelayTime(marketModel.getDelayDeductTime(configId));
            result.setShowContent(marketModel.getShowContent(programId, configId, "确认借款页"));
            result.setAdvertisement(disResult.getAdvertisement());
            // 获取营销策略样式
            String styleCode = null;
            int vipButton = 2;
            try {
                styleCode = getStyleCode(disResult, SceneCodeEnum.LOAN.getCode(), configId,
                        event.getUserId());
                vipButton = StringUtils.isNotBlank(styleCode) ? Integer.parseInt(styleCode) : 2;
            } catch (Exception e) {
                LogUtil.printLog(e, "借款首页营销,获取会员按钮样式异常");
            }
            result.setVipButton(vipButton);
            result.setStyleCode(styleCode);
            result.setPopover(disResult.getPopover());
            result.setRaiseAmount(program.getRaiseAmount());
            result.setMallMobilePrice(program.getMallMobilePrice());
            // 提额后可借款金额
            CreditCalculateVO calculateVO = marketModel.creditCalculateSimulation(event.getUserId(),
                    programId, result.getRaiseAmount());
            result.setVipCanWithdralAmount(
                    calculateVO != null ? calculateVO.getBorrowUsableSumAll() : null);
            // 折扣信息
            PlusDiscountEntity discount = marketModel.getDiscount(event.getUserId(),
                    event.getChannelId(), configId, event.getSceneCode(), disResult);
            if (discount != null && discount.getShowDiscountInfo() == 1) {
                result.setShowDiscountInfo(discount.getShowDiscountInfo());
                result.setDiscountRate(discount.getDiscountRate());
                result.setDiscountEndTime(
                        discount.getDiscountEndTime().getTime() - new Date().getTime());
                result.setDiscountPrice(
                        program.getMallMobilePrice().multiply(discount.getDiscountRate())
                                .setScale(0, RoundingMode.DOWN));
                // 营销弹窗和短信处理
                PlusLoanDiscountInfoEntity discountEntity = marketModel.handleLoanDiscountInfo(
                        event, program, discount, orders, result);
                result.setNeedDiscountAlert(discountEntity.isNeedDiscountAlert());
            }
            result.setOrderState(orders != null ? orders.getOrderState() : null);
            // 放款成功轮播记录
            result.setLoanRecords(marketModel.getLoanSuccessOrder(configId));
            if (configId == JuziPlusEnum.EXPEDITE_CARD.getCode() && orders != null
                    && orders.getCreateTime() != null) {
                result.setBlockTime(
                        LocalDateTimeUtils.discrepancySecond(new Date(), orders.getCreateTime()));
            }
            // 分流
            PlusShuntResultEntity shuntResult = shuntApplication.shuntSupplier(
                    converter.toShuntEvent(event, programId, configId,
                            result.getDiscountPrice() != null ? result.getDiscountPrice()
                                    : program.getMallMobilePrice()));
            result.setShuntSupplierId(shuntResult.getSupplierId());
            result.setSupplierName(shuntResult.getSupplierName());
            result.setContractList(shuntResult.getContractList());
            // 营销加速卡前提下处理重提客群
            if (configId == JuziPlusEnum.EXPEDITE_CARD.getCode()) {
                PlusLoanInfoEntity loanMarket = marketModel.handleLoanMarketInfo(event, orders);
                result.setResubmitCustomerGroupFlag(loanMarket.getResubmitCustomerGroupFlag());
                result.setReduceQuotaFlag(loanMarket.getReduceQuotaFlag());
                result.setWaiveLoanFlag(loanMarket.getWaiveLoanFlag());
                result.setResubmitGroupOrderAmount(loanMarket.getResubmitGroupOrderAmount());
                result.setResubmitGroupOrderPeriods(loanMarket.getResubmitGroupOrderPeriods());
                result.setCapitalId(loanMarket.getCapitalId());
            }
            result.setProgramId(programId);
            result.setProgramName(program.getName());
            result.setConfigId(program.getConfigId());
            // 填充权益列表数据
            List<PlusModelEntity> plusRightsList = Lists.newArrayList();
            List<PlusProModelEntity> plusModelList = marketModel.getPlusModelList(programId);
            if (!CollectionUtils.isEmpty(plusModelList)){
                plusModelList.forEach(o->{
                    PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(o.getModelId());
                    if (!Objects.isNull(plusModelEnum)){
                        plusRightsList.add(new PlusModelEntity(plusModelEnum.getModelName(),plusModelEnum.getShortPY()));
                    }
                });
            }
            result.setPlusRightsList(plusRightsList);

            // 桔省卡需要额外处理，不需要设置外层showType
            if (configId == JuziPlusEnum.JUS_CARD.getCode()) {
                result.setOtherCardVo(marketModel.getMarketingCardEntity(program));
            } else {
                // 其他卡设置营销卡类型
                result.setShowType(ShowTypeEnum.getShowTypeByConfigId(configId));
            }
            //是否支持后付款开通标识
            /*boolean b = programQueryModel.supportAfterPay(programId, event.getUserId(),
                    event.getChannelId());*/
            PlusProgramEntity programEntity = programQueryModel.supportPayTypes(programId, event.getUserId(),
                    event.getChannelId());
            result.setPayTypes(programEntity.getPayTypes());
            result.setFirstPayAmount(programEntity.getFirstPayAmount());
            if (!CollectionUtils.isEmpty(programEntity.getPayTypes())) {
                List<Integer> payTypes = programEntity.getPayTypes();
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                    result.setAfterPayState(1);
                } else {
                    result.setAfterPayState(2);
                }
                //如果支持首期支付计算首付金额
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                    if (discount != null && discount.getShowDiscountInfo() == 1) {
                        result.setFirstPayAmount(
                                programEntity.getFirstPayAmount().multiply(discount.getDiscountRate())
                                        .setScale(0, RoundingMode.DOWN));
                    }
                }
            }
        } catch (Exception e) {
            log.info("借款首页营销异常", e);
            iimRepository.sendImMessage("借款首页营销异常，用户id：" + event.getUserId());
        }
        return result;
    }

    @Override
    public PlusOrderListMarketEntity orderDetailMarket(PlusBillListMarketEvent event) {
        try {
            log.info("订单详情页-加速卡营销入参：{}", JSON.toJSONString(event));
            // 订单详情加速卡和订单列表页一个场景
            MarketEvent marketEvent = converter.toMarketEvent(event, SceneCodeEnum.ORDER_DETAIL);
            PlusMarketDetailVO marketDetail = magicExternalRepository.marketForScene(marketEvent);
            if (marketDetail == null) {
                log.info("订单详情页-加速卡营销，营销服务调用失败：{}", event.getUserId());
                return new PlusOrderListMarketEntity().setNoMarketReason("服务调用失败");
            }
            // 不营销
            if (marketDetail.getMarketState() == MarketStateEnum.NO_MARKET.getCode()) {
                log.info("订单详情页-加速卡营销，营销服务返回不营销：{}", event.getUserId());
                return new PlusOrderListMarketEntity().setNoMarketReason(marketDetail.getNoMarketReason());
            }
            List<ProgramBasicInfo> marketList = marketDetail.getMarketList();
            if (CollectionUtils.isEmpty(marketList)) {
                log.info("订单详情页-加速卡营销，营销服务未返回可营销列表：{}", event.getUserId());
                return new PlusOrderListMarketEntity().setNoMarketReason(marketDetail.getNoMarketReason());
            }
            ProgramBasicInfo programInfo = marketList.get(0);
            // 落地页地址拼接
            log.info("加速卡营销数据配置中心返回:{}", configProperties.suMarketingInfo);
            PlusOrderListMarketEntity confInfo = JSONObject.parseObject(
                    configProperties.suMarketingInfo, PlusOrderListMarketEntity.class);
            if (confInfo == null || confInfo.getLandingPageUrl() == null) {
                return new PlusOrderListMarketEntity();
            }
            Integer configId = programInfo.getConfigId();
            // 获取611或501状态借款单
            Orders orders = orderExternalRepository.lastOneProcessOrder(event.getUserId());
            // 桔策营销样式
            DistributionMarketEntity marketResult = engineExternalRepository.getDistributionMarketResult(
                    orders != null ? orders.getOrderSn() : null, event.getUserId(),
                    EngineCodeConstant.VIP_DOWN, event.getChannelId());
            String styleCode = getStyleCode(marketResult, SceneCodeEnum.ORDER_LIST.getCode(),
                    configId, event.getUserId());
            String landingPageUrl = confInfo.getLandingPageUrl() + programInfo.getProgramId();
            PlusOrderListMarketEntity entity = converter.toPlusOrderListMarketEntity(programInfo,
                    ShowStatusEnum.SHOW.getCode(), landingPageUrl, confInfo.getEntranceImg(),
                    styleCode);
            // 加速卡需要分流
            log.info("订单详情页-加速卡,分流开始：{}", event.getUserId());
            PlusShuntResultEntity shuntResult = shuntApplication.shuntSupplier(
                    converter.toShuntEvent(event, entity.getProgramId(), entity.getConfigId()));
            entity.setShuntSupplierId(shuntResult.getSupplierId());
            entity.setContractList(shuntResult.getContractList());
            entity.setSupplierName(shuntResult.getSupplierName());

            entity.setProgramName(programInfo.getName());
            entity.setConfigId(programInfo.getConfigId());
            entity.setRaiseAmount(programInfo.getRaiseAmount());
            // 填充权益列表数据
            List<PlusModelEntity> plusRightsList = Lists.newArrayList();
            List<PlusProModelEntity> plusModelList = marketModel.getPlusModelList(programInfo.getConfigId());
            if (!CollectionUtils.isEmpty(plusModelList)){
                plusModelList.forEach(o->{
                    PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(o.getModelId());
                    if (!Objects.isNull(plusModelEnum)){
                        plusRightsList.add(new PlusModelEntity(plusModelEnum.getModelName(),plusModelEnum.getShortPY()));
                    }
                });
            }
            entity.setPlusRightsList(plusRightsList);

            //是否支持后付款开通标识
            /*boolean b = programQueryModel.supportAfterPay(programInfo.getProgramId(), event.getUserId(),
                    event.getChannelId());
            entity.setAfterPayState(b ? 1 : 2);*/

            entity.setMallMobilePrice(programInfo.getMallMobilePrice());

            // 桔策营销样式
            DistributionMarketEntity disResult = engineExternalRepository.getDistributionMarketResult(
                    orders != null ? orders.getOrderSn() : null, event.getUserId(),
                    EngineCodeConstant.VIP_DOWN, event.getChannelId());
            // 折扣信息
            PlusDiscountEntity discount = marketModel.getDiscount(event.getUserId(),
                    event.getChannelId(), configId, PlusSceneCodeEnum.ORDER_DETAIL.getCode(), disResult);
            if (discount != null && discount.getShowDiscountInfo() == 1) {
                entity.setShowDiscountInfo(discount.getShowDiscountInfo());
                entity.setDiscountRate(discount.getDiscountRate());
                entity.setDiscountPrice(
                        programInfo.getMallMobilePrice().multiply(discount.getDiscountRate())
                                .setScale(0, RoundingMode.DOWN));
            }

            //首付支付设置
            PlusProgramEntity programEntity = programQueryModel.supportPayTypes(programInfo.getProgramId(), event.getUserId(),
                    event.getChannelId());
            entity.setPayTypes(programEntity.getPayTypes());
            entity.setFirstPayAmount(programEntity.getFirstPayAmount());
            if (!CollectionUtils.isEmpty(programEntity.getPayTypes())) {
                List<Integer> payTypes = programEntity.getPayTypes();
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_AFTER.getValue())) {
                    entity.setAfterPayState(1);
                } else {
                    entity.setAfterPayState(2);
                }
                //如果支持首期支付计算首付金额
                if (payTypes.contains(PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue())) {
                    if (discount != null && discount.getShowDiscountInfo() == 1) {
                        entity.setFirstPayAmount(
                                programEntity.getFirstPayAmount().multiply(discount.getDiscountRate())
                                        .setScale(0, RoundingMode.DOWN));
                    }
                }
            }
            return entity;
        } catch (Exception e) {
            LogUtil.printLog(e, "订单列表页加速营销异常");
            iimRepository.sendImMessage("订单列表页加速营销异常，用户id：" + event.getUserId());
        }
        return new PlusOrderListMarketEntity().setNoMarketReason("服务异常");
    }

    /**
     * 导流页营销-前置信息检查
     */
    private ProgramBasicInfo getLeadMarketResult(PlusLeadPageMarketEvent event) {
        MarketEvent marketEvent = converter.toMarketEvent(event, SceneCodeEnum.LOAN_FLOW);
        PlusMarketDetailVO marketDetail = magicExternalRepository.marketForScene(marketEvent);
        if (marketDetail == null) {
            log.info("导流页-桔省卡营销，营销服务调用失败：{}", event.getUserId());
            return null;
        }
        // 不营销
        if (marketDetail.getMarketState() == MarketStateEnum.NO_MARKET.getCode()) {
            log.info("导流页-桔省卡营销，营销服务返回不营销：{}", event.getUserId());
            return null;
        }
        List<ProgramBasicInfo> marketList = marketDetail.getMarketList();
        if (CollectionUtils.isEmpty(marketList)) {
            log.info("导流页-桔省卡营销，营销服务未返回可营销列表：{}", event.getUserId());
            return null;
        }
        return marketList.get(0);
    }

    /**
     * 融担咨询卡营销-前置信息检查
     */
    private PlusLoanConfirmRdzxMarketEntity getRdzxMarketResult(
            PlusLoanConfirmRdzxMarketEvent event) {
        MarketEvent marketEvent = converter.toMarketEvent(event, SceneCodeEnum.CONFIRM_LOAN_RD);
        PlusMarketDetailVO marketDetail = magicExternalRepository.marketForScene(marketEvent);
        if (marketDetail == null) {
            log.info("确认借款页-融担卡营销，营销服务调用失败：{}", event.getUserId());
            return new PlusLoanConfirmRdzxMarketEntity();
        }
        // 不营销
        if (marketDetail.getMarketState() == MarketStateEnum.NO_MARKET.getCode()) {
            log.info("确认借款页-融担卡营销，营销服务返回不营销：{}", event.getUserId());
            return new PlusLoanConfirmRdzxMarketEntity();
        }
        List<ProgramBasicInfo> marketList = marketDetail.getMarketList();
        if (CollectionUtils.isEmpty(marketList)) {
            log.info("确认借款页-融担卡营销，营销服务未返回可营销列表：{}", event.getUserId());
            return new PlusLoanConfirmRdzxMarketEntity();
        }
        ProgramBasicInfo programInfo = marketList.get(0);
        Integer programId = programInfo.getProgramId();
        //获取方案信息
        PlusProgramEntity program = programQueryModel.getById(programId);
        if (program == null) {
            log.info("确认借款页-融担卡营销-方案获取为空：{}", programId);
            return new PlusLoanConfirmRdzxMarketEntity();
        }
        //构建每一期的营销信息并返回
        return rdzxApplication.getPlusLoanConfirmMarketEntity(event, program);
    }
}
