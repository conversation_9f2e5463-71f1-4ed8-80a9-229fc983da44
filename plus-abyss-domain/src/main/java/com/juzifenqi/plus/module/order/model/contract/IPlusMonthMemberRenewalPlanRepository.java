package com.juzifenqi.plus.module.order.model.contract;

import com.juzifenqi.plus.module.order.model.contract.entity.PlusMonthMemberRenewalPlanEntity;
import java.util.Date;
import java.util.List;

/**
 * 会员月卡续费计划仓储接口
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
public interface IPlusMonthMemberRenewalPlanRepository {

    /**
     * 保存续费计划
     *
     * @param entity 续费计划实体
     * @return 主键ID
     */
    Integer save(PlusMonthMemberRenewalPlanEntity entity);

    /**
     * 批量插入续费计划
     *
     * @param entities 续费计划实体列表
     * @return 插入成功的记录数
     */
    Integer batchInsert(List<PlusMonthMemberRenewalPlanEntity> entities);

    /**
     * 根据订单号查询续费计划记录
     *
     * @param orderSn 会员订单号
     * @return 续费计划记录列表
     */
    List<PlusMonthMemberRenewalPlanEntity> getByOrderSn(String orderSn);

    /**
     * 根据订单号查询指定状态的续费计划记录
     *
     * @param orderSn 会员订单号
     * @param planStates 计划状态列表
     * @return 续费计划记录列表
     */
    List<PlusMonthMemberRenewalPlanEntity> getByOrderSnAndStates(String orderSn, List<Integer> planStates);

    /**
     * 分页查询续费计划记录
     *
     * @param planTime 计划生成时间（可选）
     * @param planStates 计划状态列表（可选）
     * @param pageNum 页码
     * @param pageSize 每页大小
     * @return 续费计划记录列表
     */
    List<PlusMonthMemberRenewalPlanEntity> pageQuery(Date planTime, List<Integer> planStates, 
                                                     Integer pageNum, Integer pageSize);

    /**
     * 查询续费计划记录总数
     *
     * @param planTime 计划生成时间（可选）
     * @param planStates 计划状态列表（可选）
     * @return 记录总数
     */
    Integer countQuery(Date planTime, List<Integer> planStates);

    /**
     * 根据ID查询续费计划
     *
     * @param id 主键ID
     * @return 续费计划实体
     */
    PlusMonthMemberRenewalPlanEntity getById(Integer id);

    /**
     * 更新续费计划状态
     *
     * @param id 主键ID
     * @param planState 新状态
     * @param remark 备注
     * @return 更新成功标识
     */
    Boolean updatePlanState(Integer id, Integer planState, String remark);

    /**
     * 同时更新续费计划状态、备注和实际生成时间
     *
     * @param id 主键ID
     * @param orderSn  订单号
     * @param planState 新状态
     * @param remark 备注
     * @param actualPlanTime 实际生成时间
     * @return 更新成功标识
     */
    Boolean updatePlanStateAndActualTime(Integer id,String orderSn,Integer planState, String remark, Date actualPlanTime);

    /**
     * 取消续费计划
     *
     * @param orderSn 订单号
     */
    void cancelRenewalPlan(String orderSn);
}
