package com.juzifenqi.plus.module.order.application.converter;

import com.juzifenqi.plus.enums.OrderPayActionEnum;
import com.juzifenqi.plus.enums.PlusDeductLogTypeEnum;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.module.asserts.model.event.PlusMemberCardOpenEvent;
import com.juzifenqi.plus.module.asserts.model.event.PlusProfitSendPlanEvent;
import com.juzifenqi.plus.module.asserts.model.event.ProductCheckEvent;
import com.juzifenqi.plus.module.asserts.model.event.VirtualCheckEvent;
import com.juzifenqi.plus.module.asserts.model.event.VirtualGoodsCheckEvent;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.order.adapter.event.PlusRepeatPayEvent;
import com.juzifenqi.plus.module.order.application.ao.MemberPlusSystemLogAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderCancelAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderDeductAo;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewalPlanInfoAo;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberPlusRenewPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDeductLogEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderCancelEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductResEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewalPlanInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.repay.PlusRepayRenewEntity;
import com.juzifenqi.plus.module.order.model.event.CancelRenewEvent;
import com.juzifenqi.plus.module.order.model.event.CreateDeductPlanEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductExcEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductFailEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductSuccessEvent;
import com.juzifenqi.plus.module.order.model.event.notice.PlusOrderNoticeEvent;
import com.juzifenqi.plus.module.order.model.event.order.CreateOrderBillEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCancelEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayCallbackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusProductOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.UpdOrderStateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualGoodsOrderCreateEvent;
import com.juzifenqi.plus.module.order.model.event.order.VirtualOrderCreateEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper
public interface IPlusOrderApplicationConverter {

    IPlusOrderApplicationConverter instance = Mappers.getMapper(
            IPlusOrderApplicationConverter.class);


    @Mappings({@Mapping(target = "orderEntity", source = "order"),
            @Mapping(target = "remark", ignore = true),
            @Mapping(target = "plusOrderSn", source = "order.orderSn"),
            @Mapping(target = "orderSn", source = "callbackEvent.loanOrderSn")})
    PlusMemberCardOpenEvent toPlusOrderCreateEvent(PlusProgramEntity programEntity,
            PlusOrderEntity order, PlusOrderPayCallbackEvent callbackEvent);

    @Mappings({@Mapping(target = "orderEntity", source = "order"),
            @Mapping(target = "remark", ignore = true),
            @Mapping(target = "plusOrderSn", source = "order.orderSn"),
            @Mapping(target = "orderSn", source = "createEvent.createOrderContext.loanOrderSn"),
            @Mapping(target = "userId", source = "order.userId"),
            @Mapping(target = "channelId", source = "order.channelId")})
    PlusMemberCardOpenEvent toPlusOrderCreateEvent(PlusProgramEntity programEntity,
            PlusOrderEntity order, PlusOrderCreateEvent createEvent);

    PlusOrderAo toPlusOrderAo(PlusOrderEntity plusOrderEntity);

    PlusOrderCancelAo toPlusOrderCancelAo(PlusOrderCancelEntity plusOrderCancelEntity);

    @Mappings({@Mapping(target = "plusOrderSn", source = "callbackEvent.orderSn"),
            @Mapping(target = "userId", source = "callbackEvent.memberId"),
            @Mapping(target = "channelId", source = "plusOrderEntity.channelId")})
    PlusRepeatPayEvent toPlusRepeatPayEvent(PlusOrderPayCallbackEvent callbackEvent,
            PlusOrderEntity plusOrderEntity);

    @Mappings({@Mapping(target = "plusOrderSn", source = "plusOrderEntity.orderSn"),
            @Mapping(target = "userId", source = "createEvent.userId"),
            @Mapping(target = "channelId", source = "plusOrderEntity.channelId"),
            @Mapping(target = "programId", source = "createEvent.programId"),
            @Mapping(target = "programName", source = "plusOrderEntity.programName")})
    PlusRepeatPayEvent toPlusRepeatPayEvent(PlusOrderCreateEvent createEvent,
            PlusOrderEntity plusOrderEntity);

    @Mappings({@Mapping(target = "plusOrderSn", source = "plusOrderEntity.orderSn"),
            @Mapping(target = "orderSn", source = "event.createOrderContext.loanOrderSn"),
            @Mapping(target = "configId", source = "plusOrderEntity.configId"),
            @Mapping(target = "userId", source = "plusOrderEntity.userId"),
            @Mapping(target = "channelId", source = "plusOrderEntity.channelId")})
    PlusOrderRelationCreateEvent toPlusOrderRelationCreateEvent(PlusOrderCreateEvent event,
            PlusOrderEntity plusOrderEntity);

    @Mappings({@Mapping(target = "plusOrderSn", source = "plusOrderEntity.orderSn"),
            @Mapping(target = "configId", source = "plusOrderEntity.configId"),
            @Mapping(target = "userId", source = "plusOrderEntity.userId"),
            @Mapping(target = "channelId", source = "plusOrderEntity.channelId")})
    PlusOrderRelationCreateEvent toPlusOrderRelationCreateEvent(PlusOrderEntity plusOrderEntity);


    PlusOrderDeductAo toPlusDeductAo(PlusOrderDeductResEntity deductEntity);

    @Mappings({@Mapping(target = "renewPrice", source = "renewPlan.renewAmount")})
    PlusOrderCreateEvent toPlusOrderCreateEvent(MemberPlusRenewPlanEntity renewPlan,
            Integer payType, Integer renew, String ascribeTo);


    @Mappings({@Mapping(target = "planTime", source = "deductTime"),
            @Mapping(target = "plusOrderSn", source = "plusOrderSn")})
    CreateDeductPlanEvent toCreateDeductPlanEvent(Date deductTime,
            MemberPlusRenewPlanEntity renewPlan, String plusOrderSn);

    @Mappings({@Mapping(target = "userId", source = "param.userId"),
            @Mapping(target = "orderSn", source = "param.orderSn"),
            @Mapping(target = "plusOrderSn", source = "param.plusOrderSn"),
            @Mapping(target = "logType", source = "param.deductFlag.code"),
            @Mapping(target = "logCode", source = "logTypeEnum.code"),
            @Mapping(target = "remark", source = "remark")})
    PlusDeductLogEntity checkToDeductLogEntity(PlusDeductEvent param,
            PlusDeductLogTypeEnum logTypeEnum, String remark);

    PlusRenewInfoAo toPlusRenewInfoAo(PlusRenewInfoEntity entity);

    PlusDeductSuccessEvent toPlusDeductSuccessEvent(PlusDeductEvent deductEvent);

    PlusDeductFailEvent toPlusDeductFailEvent(PlusDeductEvent deductEvent);

    PlusDeductExcEvent toPlusDeductExcEvent(PlusDeductEvent deductEvent);

    VirtualCheckEvent toVirtualCheckEvent(VirtualOrderCreateEvent createEvent);

    VirtualGoodsCheckEvent toVirtualGoodsCheckEvent(VirtualGoodsOrderCreateEvent createEvent);

    ProductCheckEvent toPlusProductCheckEvent(PlusProductOrderCreateEvent createEvent);

    @Mappings({@Mapping(target = "orderSn", source = "orderId"),
            @Mapping(target = "orderAmount", source = "moneyOrder")})
    PlusOrderAo toPlusOrderAo(OrderEntity order);

    PlusOrderNoticeEvent toPlusOrderNoticeEvent(PlusOrderEntity order, Integer noticeType);

    PlusOrderCancelEntity toPlusOrderCancelEntity(PlusOrderCancelAo order);

    CancelRenewEvent toCancelRenewEvent(String orderSn, String remark, Integer flag);

    @Mappings({@Mapping(target = "plusOrderSn", source = "event.orderSn"),
            @Mapping(target = "cancelType", source = "cancelType"),
            @Mapping(target = "cancelReason", source = "event.changeReasonCode"),
            @Mapping(target = "channelId", source = "order.channelId"),
            @Mapping(target = "optUserId", source = "event.optUserId"),
            @Mapping(target = "optUserName", source = "event.optUserName"),
            @Mapping(target = "userId", source = "order.userId"),
            @Mapping(target = "configId", source = "order.configId"),
            @Mapping(target = "programId", source = "order.programId"),
            @Mapping(target = "saveNoticeTask", source = "notice")})
    PlusOrderCancelEvent toPlusOrderCancelEvent(UpdOrderStateEvent event, PlusOrderEntity order,
            Integer cancelType, boolean notice);

    List<MemberPlusSystemLogAo> toMemberPlusSystemLogAoList(
            List<MemberPlusSystemLogEntity> entityList);

    List<PlusOrderInfoAo> toPlusOrderInfoAoList(List<PlusOrderEntity> entityList);


    PlusOrderCreateEvent toPlusOrderCreateEvent(Integer renew, Integer programId, Integer channelId,
            BigDecimal renewPrice, Integer userId, Integer payType);

    @Mappings({@Mapping(target = "repayRenewId", source = "renewInfo.renewInfo.id"),
            @Mapping(target = "bankId", source = "renewInfo.renewInfo.bankId"),
            @Mapping(target = "userId", source = "renewInfo.renewInfo.userId"),
            @Mapping(target = "configId", source = "renewInfo.renewInfo.configId")})
    PlusDeductEvent toPlusDeductEvent(PlusRepayRenewEntity renewInfo, String tradeName,
            String plusOrderSn);

    @Mappings({@Mapping(target = "plusOrderSn", source = "order.orderSn"),
            @Mapping(target = "cancelType", source = "cancelType"),
            @Mapping(target = "cancelReason", source = "cancelReason"),
            @Mapping(target = "channelId", source = "order.channelId"),
            @Mapping(target = "optUserId", source = "optUserId"),
            @Mapping(target = "optUserName", source = "optUserName"),
            @Mapping(target = "userId", source = "order.userId"),
            @Mapping(target = "configId", source = "order.configId"),
            @Mapping(target = "programId", source = "order.programId")})
    PlusOrderCancelEvent toPlusOrderCancelEvent(PlusOrderEntity order, Integer cancelType,
            Integer cancelReason, Integer optUserId, String optUserName);

    @Mappings({@Mapping(target = "userId", source = "param.userId"),
            @Mapping(target = "orderSn", source = "param.orderSn"),
            @Mapping(target = "plusOrderSn", source = "param.plusOrderSn"),
            @Mapping(target = "logType", source = "logType"),
            @Mapping(target = "logCode", source = "logTypeEnum.code"),
            @Mapping(target = "remark", source = "remark")})
    PlusDeductLogEntity excToDeductLogEntity(PlusDeductExcEvent param, Integer logType,
            PlusDeductLogTypeEnum logTypeEnum, String remark);

    PlusDeductEvent toPlusDeductEvent(PlusOrderDeductPlanEntity entity, PlusPayTypeEnum deductFlag);

    @Mappings({@Mapping(target = "id", source = "orderId")})
    PlusOrderInfoAo toPlusOrderInfoAo(PlusOrderEntity entity);

    @Mappings({@Mapping(target = "orderSn", source = "orderSn"),
            @Mapping(target = "programId", source = "programId"),
            @Mapping(target = "configId", source = "configId"),
            @Mapping(target = "modelIdList", source = "modelIdList")})
    PlusProfitSendPlanEvent toPlusProfitSendPlanEvent(String orderSn, Integer programId,
            Integer configId, List<Integer> modelIdList);

    @Mappings({@Mapping(target = "orderSn", source = "event.plusOrderSn"),
            @Mapping(target = "batchCancel", source = "event.batchOpt"),
            @Mapping(target = "needNotice", source = "event.saveNoticeTask")})
    PlusOrderRefundInfoEntity toPlusOrderRefundInfoEntity(PlusOrderCancelEvent event,
            String refundSerialNo, Integer refundState, Integer refundType);

    PlusDeductEvent toPlusDeductEvent(PlusOrderEntity entity, String tradeName,
            OrderPayActionEnum payAction);

    @Mapping(target = "plusOrderSn", source = "orderSn")
    CreateOrderBillEvent toCreateOrderBillEvent(PlusOrderEntity order);

    PlusRenewalPlanInfoAo toPlusRenewalPlanInfoAo(PlusRenewalPlanInfoEntity renewalPlanInfo);
}
