package com.juzifenqi.plus.module.order.application;

import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewalPlanInfoAo;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.event.PlusRenewInfoEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRenewalPlanInfoEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationEvent;
import java.util.List;

/**
 * 订单查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/23 11:34
 */
public interface IPlusOrderQueryApplication {

    /**
     * 获取续费信息
     */
    PlusRenewInfoAo getRenewInfo(PlusRenewInfoEvent event);

    /**
     * 获取订单关联信息
     */
    List<PlusOrderRelationEntity> getOrderRelation(PlusOrderRelationEvent event);

    /**
     * 获取待支付后付款订单信息
     */
    List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId,
            Integer programId);

    /**
     * 根据条件查询用户订单列表
     */
    List<PlusOrderEntity> getUserPlusOrderList(PlusOrderQueryReq req);

    /**
     * 获取待支付后付款订单信息
     */
    List<PlusOrderEntity> getUserWaitPayOrderList(PlusOrderQueryReq req);

    /**
     * 划扣失败预警
     */
    void deductError(Integer beforeMinutes, Integer warningNumber);


    PlusOrderEntity getUserOutPlusOrder(PlusOutOrderQueryReq req);

    /**
     * 获取会员月卡续费计划信息
     */
    PlusRenewalPlanInfoAo getRenewalPlanInfo(PlusRenewalPlanInfoEvent plusRenewalPlanInfoEvent);

}
