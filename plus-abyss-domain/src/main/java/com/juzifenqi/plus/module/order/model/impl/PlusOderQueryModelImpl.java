package com.juzifenqi.plus.module.order.model.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.member.entity.member.MemberInfo;
import com.juzifenqi.order.dto.OrderSimpleInfoDTO;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq;
import com.juzifenqi.plus.dto.req.member.MemberQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.RenewStateEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.MemberPlusQueryModel;
import com.juzifenqi.plus.module.asserts.model.MemberProfitsQueryModel;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoDetailRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberUseProductRecordEntity;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.common.IMemberExternalRepository;
import com.juzifenqi.plus.module.common.IMemberPlusSystemLogRepository;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.common.repository.external.acl.AuthExternalRepositoryAcl;
import com.juzifenqi.plus.module.market.model.contract.entity.LoanSuccessOrderEntity;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.ILoanSuccessOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.IMemberPlusRenewPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusMonthMemberRenewalPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderDeductPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRelationRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberPlusRenewPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusMonthMemberRenewalPlanEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderExtInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusProductOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewalPlanInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IOrderExternalRepository;
import com.juzifenqi.plus.module.order.model.converter.IPlusOrderModelConverter;
import com.juzifenqi.plus.module.order.model.event.PlusRenewInfoEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRenewalPlanInfoEvent;
import com.juzifenqi.plus.utils.RedisUtils;
import com.jzfq.auth.core.entity.AuthApproval;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 会员订单查询相关
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/6 16:55
 */
@Slf4j
@Service
public class PlusOderQueryModelImpl implements PlusOrderQueryModel {

    private final IPlusOrderModelConverter converter = IPlusOrderModelConverter.instance;

    @Autowired
    private IPlusOrderRepository            iPlusOrderRepository;
    @Autowired
    private IPlusOrderRelationRepository    orderRelationRepository;
    @Autowired
    private IMemberPlusSystemLogRepository  memberPlusSystemLogRepository;
    @Autowired
    private MemberPlusQueryModel            memberPlusQueryModel;
    @Autowired
    private IMemberPlusInfoDetailRepository plusInfoDetailRepository;
    @Autowired
    private IOrderExternalRepository        orderExternalRepository;
    @Autowired
    private ILoanSuccessOrderRepository     loanOrderRepository;
    @Autowired
    private MemberProfitsQueryModel         profitsQueryModel;
    @Autowired
    private IMemberPlusRenewPlanRepository  memberPlusRenewPlanRepository;
    @Autowired
    private RedisUtils                     redisUtils;
    @Autowired
    private IIMRepository                  imRepository;
    @Autowired
    private IPlusOrderDeductPlanRepository deductPlanRepository;
    @Autowired
    private ConfigProperties configProperties;
    @Autowired
    private AuthExternalRepositoryAcl authExternalRepositoryAcl;
    @Autowired
    private IMemberExternalRepository memberExternalRepository;
    @Autowired
    private IPlusMonthMemberRenewalPlanRepository plusMonthMemberRenewalPlanRepository;


    @Override
    public PlusOrderEntity getByOrderSn(String orderSn) {
        return iPlusOrderRepository.getByPlusOrderSn(orderSn);
    }

    @Override
    public PlusOrderExtInfoEntity getOrderExtInfo(String orderSn) {
        return iPlusOrderRepository.getOrderExtInfo(orderSn);
    }

    /**
     * 计算订单笔数
     */
    @Override
    public Integer countByOrderListAndState(List<String> orderList, Integer orderState) {
        return iPlusOrderRepository.countByOrderListAndState(orderList, orderState);
    }

    /**
     * 计算订单支付金额
     */
    @Override
    public BigDecimal sumPayAmountByOrderListAndState(List<String> orderList, Integer orderState) {
        if (CollectionUtils.isEmpty(orderList)) {
            return BigDecimal.ZERO;
        }
        return iPlusOrderRepository.sumPayAmountByOrderListAndState(orderList, orderState);
    }

    /**
     * 获取会员单关联的借款单
     */
    @Override
    public String getRelationLoanOrderSn(String plusOrderSn, Integer businessType) {
        PlusOrderRelationEntity orderRel = orderRelationRepository.getByBusinessTypeAndPlusOrderSn(
                businessType, plusOrderSn);
        return orderRel != null ? orderRel.getOrderSn() : null;
    }

    @Override
    public List<PlusOrderRelationEntity> getRelationByLoanOrderSn(List<String> orderSns,
            Integer businessType) {
        return orderRelationRepository.listByBusinessTypeAndOrderSn(businessType, orderSns);
    }

    @Override
    public List<PlusOrderRelationEntity> getRelationByPlusOrderSn(List<String> orderSns,
            Integer businessType) {
        return orderRelationRepository.listByBusinessTypeAndPlusOrderSn(businessType, orderSns);
    }

    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId,
            Integer programId) {
        return iPlusOrderRepository.getUserWaitPayOrderList(userId, channelId, programId);
    }

    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId) {
        return iPlusOrderRepository.getUserWaitPayOrderList(userId, channelId);
    }

    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderListByCondition(Integer userId, Integer channelId, List<Integer> payTypes) {
        return iPlusOrderRepository.getUserWaitPayOrderListByCondition(userId, channelId, payTypes);
    }

    @Override
    public List<PlusOrderEntity> pageList(PlusOrderInfoQueryReq req) {
        if (req.getPlusState() != null) {
            log.info("付费会员订单列表-会员状态不为空数据获取");
            List<PlusOrderEntity> resultList = iPlusOrderRepository.pageListByState(req);
            //取消的订单设置取消操作人
            if (!CollectionUtils.isEmpty(resultList)) {
                //获取取消信息
                List<String> orderSns = resultList.stream().map(PlusOrderEntity::getOrderSn)
                        .collect(Collectors.toList());
                List<MemberPlusSystemLogEntity> cancelInfos = memberPlusSystemLogRepository.getLastCancelInfos(
                        orderSns);
                Map<String, MemberPlusSystemLogEntity> allLogInfos = cancelInfos.stream().collect(
                        Collectors.toMap(MemberPlusSystemLogEntity::getOrderSn, Function.identity(),
                                (oldValue, newValue) -> newValue));
                // 20230509 zjf 新增联名卡标识
                List<PlusOrderExtInfoEntity> extInfos = iPlusOrderRepository.getOrderExtInfoList(
                        orderSns);
                Map<String, PlusOrderExtInfoEntity> extMap = extInfos.stream().collect(
                        Collectors.toMap(PlusOrderExtInfoEntity::getOrderSn, Function.identity(),
                                (oldValue, newValue) -> newValue));
                resultList.forEach(resInfo -> {
                    //取消的订单设置取消操作人
                    if (resInfo.getOrderState() == PlusOrderStateEnum.CANCELED.getCode()) {
                        MemberPlusSystemLogEntity logInfo = allLogInfos.get(resInfo.getOrderSn());
                        resInfo.setCancelOptName(
                                logInfo != null ? logInfo.getOperatingName() : null);
                    }
                    // 20230509 zjf 联名卡标识
                    PlusOrderExtInfoEntity extInfo = extMap.get(resInfo.getOrderSn());
                    if (extInfo != null) {
                        resInfo.setBusinessType(extInfo.getBusinessType());
                        resInfo.setLmkPlusAmount(extInfo.getOrderAmount());
                    }
                });
            }
            return resultList;
        }
        log.info("付费会员订单列表-会员状态为空数据获取");
        List<PlusOrderEntity> resultList = iPlusOrderRepository.pageList(req);
        // 赋值关联的会员信息（通过单号去查-expire_member_plus_info_detail和member_plus_info_detail）
        if (!CollectionUtils.isEmpty(resultList)) {
            List<String> orderSns = resultList.stream().map(PlusOrderEntity::getOrderSn)
                    .collect(Collectors.toList());
            //获取用户信息
            List<MemberPlusInfoDetailEntity> infos = memberPlusQueryModel.getInfoByOrders(orderSns);
            List<MemberPlusInfoDetailEntity> expireInfos = plusInfoDetailRepository.getExpireInfoByOrders(
                    orderSns);
            Map<String, MemberPlusInfoDetailEntity> allInfos = new HashMap<>(orderSns.size() * 2);
            infos.forEach(info -> allInfos.put(info.getOrderSn(), info));
            expireInfos.forEach(expire -> allInfos.put(expire.getOrderSn(), expire));
            //获取取消信息
            List<MemberPlusSystemLogEntity> cancelInfos = memberPlusSystemLogRepository.getLastCancelInfos(
                    orderSns);
            Map<String, MemberPlusSystemLogEntity> allLogInfos = cancelInfos.stream().collect(
                    Collectors.toMap(MemberPlusSystemLogEntity::getOrderSn, Function.identity(),
                            (oldValue, newValue) -> newValue));
            // 20230509 zjf 新增联名卡标识
            List<PlusOrderExtInfoEntity> extInfos = iPlusOrderRepository.getOrderExtInfoList(
                    orderSns);
            Map<String, PlusOrderExtInfoEntity> extMap = extInfos.stream().collect(
                    Collectors.toMap(PlusOrderExtInfoEntity::getOrderSn, Function.identity(),
                            (oldValue, newValue) -> newValue));
            //处理返回值
            resultList.forEach(resultOrder -> {
                //设置会员状态
                MemberPlusInfoDetailEntity targetInfo = allInfos.get(resultOrder.getOrderSn());
                resultOrder.setPlusState(targetInfo == null ? -1 : targetInfo.getJxStatus());
                //取消的订单设置取消操作人
                if (resultOrder.getOrderState() == PlusOrderStateEnum.CANCELED.getCode()) {
                    MemberPlusSystemLogEntity logInfo = allLogInfos.get(resultOrder.getOrderSn());
                    resultOrder.setCancelOptName(
                            logInfo != null ? logInfo.getOperatingName() : null);
                }
                // 20230509 zjf 联名卡标识
                PlusOrderExtInfoEntity extInfo = extMap.get(resultOrder.getOrderSn());
                if (extInfo != null) {
                    resultOrder.setBusinessType(extInfo.getBusinessType());
                    resultOrder.setLmkPlusAmount(extInfo.getOrderAmount());
                }
            });
        }
        return resultList;
    }

    @Override
    public Integer pageListCount(PlusOrderInfoQueryReq req) {
        return iPlusOrderRepository.pageListCount(req);
    }


    @Override
    public List<PlusOrderEntity> getWaitPayOrderList(List<String> orders) {
        return iPlusOrderRepository.getWaitPayOrderList(orders);
    }

    @Override
    public List<PlusOrderEntity> getByOrderSn(List<String> orders) {
        return iPlusOrderRepository.getOrderList(orders);
    }

    @Override
    public PlusOrderEntity getUserWaitPayOrder(Integer userId, Integer configId) {
        MemberInfo memberInfo = memberExternalRepository.getById(userId);
        Integer channel = memberInfo.getChannelId();
        if (configProperties.authChannel.contains(channel.toString())) {
            AuthApproval authState = authExternalRepositoryAcl.getAuthState(userId, channel);
            return iPlusOrderRepository.getUserWaitPayOrderAfterAuthTime(userId, configId, authState.getSuccessTime());
        }
        return iPlusOrderRepository.getUserWaitPayOrder(userId, configId);
    }

    @Override
    public List<PlusProductOrderEntity> getProductOrderList(String plusOrderSn, Integer modelId) {
        List<MemberUseProductRecordEntity> list = profitsQueryModel.getUserBuyOrderRecords(
                plusOrderSn, modelId);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<PlusProductOrderEntity> entities = converter.toPlusProductOrderEntityList(list);
        // 获取订单状态
        for (PlusProductOrderEntity entity : entities) {
            if (StringUtils.isBlank(entity.getOrderSn())) {
                continue;
            }
            try {
                OrderSimpleInfoDTO order = orderExternalRepository.getByOrderSn(
                        entity.getOrderSn());
                if (order == null) {
                    continue;
                }
                entity.setOrderStateDesc(
                        orderExternalRepository.getOrderStateDesc(order.getOrderState()));
            } catch (Exception e) {
                LogUtil.printLog(e, "获取会员商品订单信息调用订单中心接口异常");
            }
        }
        return entities;
    }

    @Override
    public List<PlusOrderEntity> getPayRecordByProgram(Integer programId) {
        return iPlusOrderRepository.getPayRecordByProgram(programId);
    }

    @Override
    public List<PlusOrderEntity> getSevenPlusOrder(Integer userId, Integer channelId) {
        List<PlusOrderEntity> orderEntities;
        if (configProperties.authChannel.contains(channelId.toString())) {
            orderEntities = iPlusOrderRepository.getPlusOrderByAuthTime(userId,
                    channelId, JuziPlusEnum.NEW_JUXP_CARD.getCode(), JuziPlusEnum.QUOTA_CARD.getCode(),
                    JuziPlusEnum.EXPEDITE_CARD.getCode(), JuziPlusEnum.REPAY_CARD.getCode(),
                    JuziPlusEnum.AUTH_CARD.getCode(), JuziPlusEnum.RESUBMIT_CARD.getCode(),
                    JuziPlusEnum.AUTHFAIL_CARD.getCode(), JuziPlusEnum.SUCCESS_CARD.getCode(),
                    JuziPlusEnum.DOWNRATE_CARD.getCode(), JuziPlusEnum.YITONG_CARD.getCode(),
                    JuziPlusEnum.HYYK_CARD.getCode());
        } else {
            orderEntities = iPlusOrderRepository.getSevenPlusOrder(userId, channelId,
                    Arrays.asList(JuziPlusEnum.NEW_JUXP_CARD.getCode(),
                            JuziPlusEnum.QUOTA_CARD.getCode(), JuziPlusEnum.EXPEDITE_CARD.getCode(),
                            JuziPlusEnum.REPAY_CARD.getCode(), JuziPlusEnum.AUTH_CARD.getCode(),
                            JuziPlusEnum.RESUBMIT_CARD.getCode(), JuziPlusEnum.AUTHFAIL_CARD.getCode(),
                            JuziPlusEnum.SUCCESS_CARD.getCode(), JuziPlusEnum.DOWNRATE_CARD.getCode(),
                            JuziPlusEnum.YITONG_CARD.getCode(), JuziPlusEnum.HYYK_CARD.getCode()));
        }
        return orderEntities;
    }

    @Override
    public PlusRenewInfoEntity getRenewInfo(PlusRenewInfoEvent event) {
        // 当前会员续费信息
        MemberPlusInfoEntity memberPlusInfo = memberPlusQueryModel.getMemberPlusInfo(
                event.getUserId(), event.getChannelId(), event.getConfigId());
        if (memberPlusInfo == null) {
            return null;
        }
        PlusRenewInfoEntity entity = new PlusRenewInfoEntity();
        entity.setRenewState(memberPlusInfo.getRenewState());
        entity.setRemark(memberPlusInfo.getRemark());
        // 当前周期续费计划
        MemberPlusRenewPlanEntity currentRenewPlan = memberPlusRenewPlanRepository.getCurrentPeriod(
                event.getChannelId(), event.getUserId(), event.getConfigId(),
                RenewStateEnum.RENEWED.getCode());
        // 续费计划为空，可能是首单还没有支付，没有生成续费计划
        if (currentRenewPlan == null) {
            PlusOrderEntity plusOrderEntity = iPlusOrderRepository.getByPlusOrderSn(
                    event.getOrderSn());
            if (plusOrderEntity == null) {
                // 订单为空，无法根据订单状态告知用户对应的取消续费文案，数据有问题，不能取消续费
                return null;
            }
            entity.setCurrentOrderState(plusOrderEntity.getOrderState());
            return entity;
        }
        // 续费计划不为空，获取当前周期续费订单
        PlusOrderEntity plusOrderEntity = iPlusOrderRepository.getByPlusOrderSn(
                currentRenewPlan.getPlusOrderSn());
        if (plusOrderEntity == null) {
            // 订单为空，无法根据订单状态告知用户对应的取消续费文案，数据有问题，不能取消续费
            return null;
        }
        entity.setCurrentOrderState(plusOrderEntity.getOrderState());
        return entity;
    }

    @Override
    public List<LoanSuccessOrderEntity> getLoanSuccessOrder() {
        try {
            log.info("获取借款单放款成功前50条数据开始");
            //缓存中获取数据
            String redisKey = RedisConstantPrefix.LOAN_ORDER_RECORDS;
            String cacheValue = redisUtils.get(redisKey);
            if (!StringUtils.isEmpty(cacheValue)) {
                if (CommonConstant.DEFAULT_NULL.equals(cacheValue)) {
                    log.info("获取借款单放款成功前50条数据-命中缓存空数据");
                    return null;
                }
                List<LoanSuccessOrderEntity> redisList = JSON.parseArray(cacheValue,
                        LoanSuccessOrderEntity.class);
                if (!CollectionUtils.isEmpty(redisList)) {
                    log.info("获取借款单放款成功前50条数据命中缓存,直接返回");
                    return redisList;
                }
            }
            log.info("”获取借款单放款成功前50条数据查询数据库开始");
            //增加轮播数据
            List<LoanSuccessOrderEntity> loanRecords = loanOrderRepository.getLoanSuccessOrder();
            if (CollectionUtils.isEmpty(loanRecords)) {
                //存到redis中空数据
                log.info("获取借款单放款成功前50条数据查询数据库为空");
                redisUtils.setEx(redisKey, CommonConstant.DEFAULT_NULL, 10, TimeUnit.SECONDS);
                return null;
            }
            loanRecords.forEach(loanRecord -> {
                String orderSn = loanRecord.getOrderSn();
                loanRecord.setOrderSn(
                        "****" + orderSn.substring(orderSn.length() - CommonConstant.FOUR));
            });
            redisUtils.setEx(redisKey, JSON.toJSONString(loanRecords), 3, TimeUnit.HOURS);
            return loanRecords;
        } catch (Exception e) {
            LogUtil.printLog(e, "获取借款单放款成功前50条数据异常");
        }
        return null;
    }

    /**
     * 获取7天内购买的有效会员订单信息
     */
    @Override
    public List<PlusOrderEntity> getMemberSevenOrderList(MemberQueryReq req) {
        Integer channelId = req.getChannelId();
        Integer userId = req.getUserId();
        if (configProperties.authChannel.contains(channelId.toString())) {
            AuthApproval authState = authExternalRepositoryAcl.getAuthState(userId, channelId);
            return iPlusOrderRepository.getPlusOrderInfoListByAuthTime(userId, channelId, authState.getSuccessTime());
        }
        return iPlusOrderRepository.getMemberSevenOrderList(userId, channelId);
    }

    /**
     * 根据条件查询用户订单列表
     */
    @Override
    public List<PlusOrderEntity> getUserPlusOrderList(PlusOrderQueryReq req) {
        return iPlusOrderRepository.getUserOrderList(req);
    }

    /**
     * 获取待支付后付款订单信息
     */
    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderList(PlusOrderQueryReq req) {
        return iPlusOrderRepository.getUserWaitPayOrderList(req);
    }

    @Override
    public void deductError(Integer beforeMinutes, Integer warningNumber) {
        List<Integer> deductErrorList = deductPlanRepository.getDeductError(beforeMinutes);
        int errorCount = deductErrorList.size();
        log.info("划扣失败数量:{},预警数量:{}", errorCount, warningNumber);
        if (errorCount >= warningNumber) {
            Map<Integer, Long> deductErrorMap = deductErrorList.stream()
                    .collect(Collectors.groupingBy(d -> d, Collectors.counting()));
            deductErrorMap.forEach((k, v) -> log.info("划扣失败会员卡类型:{},失败数量:{}", k, v));
            imRepository.sendImMessage(
                    "划扣失败数量达到预警,失败数量:" + errorCount + ",预警数量:" + warningNumber);
        }
    }

    @Override
    public List<PlusOrderEntity> getPlusOrderListByIdLimit(Integer startId, Integer maxId, Integer batchSize) {
        return iPlusOrderRepository.getPlusOrderListByIdLimit(startId,maxId,batchSize);
    }

    @Override
    public List<PlusOrderEntity> getPlusOrderByIds(List<Integer> ids) {
        return iPlusOrderRepository.getPlusOrderByIds(ids);
    }

    @Override
    public PlusOrderEntity getPlusOutOrder(PlusOutOrderQueryReq req) {
        return iPlusOrderRepository.getPlusOutOrder(req);
    }

    @Override
    public PlusRenewalPlanInfoEntity getRenewalPlanInfo(PlusRenewalPlanInfoEvent event) {
        if (event == null || StringUtils.isBlank(event.getOrderSn())) {
            throw new PlusAbyssException("会员单号不能为空");
        }
        PlusOrderEntity plusOrder = iPlusOrderRepository.getByPlusOrderSn(event.getOrderSn());
        if (plusOrder == null) {
            throw new PlusAbyssException("无效的订单号");
        }
        MemberInfo memberInfo = memberExternalRepository.getById(plusOrder.getUserId());
        List<PlusMonthMemberRenewalPlanEntity> renewalPlanEntities =
                plusMonthMemberRenewalPlanRepository.getByOrderSn(plusOrder.getOrderSn());
        PlusRenewalPlanInfoEntity entity = new PlusRenewalPlanInfoEntity();
        entity.setUserId(memberInfo.getId());
        entity.setChannel(memberInfo.getChannel());
        entity.setUserName(memberInfo.getName());
        if (!CollectionUtils.isEmpty(renewalPlanEntities)) {
            PlusMonthMemberRenewalPlanEntity renewalPlanEntity = renewalPlanEntities.get(0);
            entity.setMonthNo(renewalPlanEntity.getMonthNo());
            entity.setCreateTime(renewalPlanEntity.getCreateTime());
            entity.setRenewalPlanList(renewalPlanEntities);
        }
        return entity;
    }
}
