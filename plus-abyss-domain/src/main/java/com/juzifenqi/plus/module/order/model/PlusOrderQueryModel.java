package com.juzifenqi.plus.module.order.model;

import com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq;
import com.juzifenqi.plus.dto.req.member.MemberQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq;
import com.juzifenqi.plus.module.market.model.contract.entity.LoanSuccessOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderExtInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusProductOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewalPlanInfoEntity;
import com.juzifenqi.plus.module.order.model.event.PlusRenewInfoEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRenewalPlanInfoEvent;

import java.math.BigDecimal;
import java.util.List;

/**
 * 订单查询领域
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/25 17:32
 */
public interface PlusOrderQueryModel {

    /**
     * 获取订单信息
     */
    PlusOrderEntity getByOrderSn(String orderSn);

    /**
     * 根据订单号获取扩展数据
     */
    PlusOrderExtInfoEntity getOrderExtInfo(String orderSn);

    /**
     * 计算订单笔数
     */
    Integer countByOrderListAndState(List<String> orderList, Integer orderState);

    /**
     * 计算订单支付金额
     */
    BigDecimal sumPayAmountByOrderListAndState(List<String> orderList, Integer orderState);

    /**
     * 获取会员单关联的借款单
     */
    String getRelationLoanOrderSn(String plusOrderSn, Integer businessType);

    /**
     * 获取借款单关联的信息
     */
    List<PlusOrderRelationEntity> getRelationByLoanOrderSn(List<String> orderSns,
            Integer businessType);

    /**
     * 获取会员单关联的信息
     */
    List<PlusOrderRelationEntity> getRelationByPlusOrderSn(List<String> orderSns,
            Integer businessType);

    /**
     * 获取用户待支付的后付款订单列表
     */
    List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId,
            Integer programId);

    /**
     * 获取用户待支付的后付款订单列表
     */
    List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId);

    List<PlusOrderEntity> getUserWaitPayOrderListByCondition(Integer userId, Integer channelId, List<Integer> payTypes);

    /**
     * 查询会员订单列表数据
     */
    List<PlusOrderEntity> pageList(PlusOrderInfoQueryReq req);

    /**
     * 查询会员订单列表数据count
     */
    Integer pageListCount(PlusOrderInfoQueryReq req);

    /**
     * 获取待支付的后付款订单列表
     */
    List<PlusOrderEntity> getWaitPayOrderList(List<String> orders);

    /**
     * 根据会员订单号批量获取
     */
    List<PlusOrderEntity> getByOrderSn(List<String> orders);

    /**
     * 获取用户某个会员卡待支付的后付款订单
     */
    PlusOrderEntity getUserWaitPayOrder(Integer userId, Integer configId);

    /**
     * 获取会员订单某个权益下单的会员商品订单信息
     */
    List<PlusProductOrderEntity> getProductOrderList(String plusOrderSn, Integer modelId);

    /**
     * 查询最新的40条支付成功订单列表
     */
    List<PlusOrderEntity> getPayRecordByProgram(Integer programId);

    /**
     * 获取用户7天内买过的有效会员订单
     */
    List<PlusOrderEntity> getSevenPlusOrder(Integer userId, Integer channelId);

    /**
     * 获取续费信息
     */
    PlusRenewInfoEntity getRenewInfo(PlusRenewInfoEvent event);

    /**
     * 获取放款成功借款单前50条数据
     */
    List<LoanSuccessOrderEntity> getLoanSuccessOrder();

    /**
     * 获取7天内购买的有效会员订单信息
     */
    List<PlusOrderEntity> getMemberSevenOrderList(MemberQueryReq req);

    /**
     * 根据条件查询用户订单列表
     */
    List<PlusOrderEntity> getUserPlusOrderList(PlusOrderQueryReq req);

    /**
     * 获取待支付后付款订单信息
     */
    List<PlusOrderEntity> getUserWaitPayOrderList(PlusOrderQueryReq req);

    /**
     * 划扣失败预警
     */
    void deductError(Integer beforeMinutes, Integer warningNumber);

    /**
     * 根据开始id，最大id以及批次大小查询记录
     * @param startId
     * @param maxId
     * @param batchSize
     * @return
     */
    List<PlusOrderEntity> getPlusOrderListByIdLimit(Integer startId,Integer maxId,Integer batchSize);

    /**
     * 根据id的集合查询记录
     * @param ids
     * @return
     */
    List<PlusOrderEntity> getPlusOrderByIds(List<Integer> ids);


    PlusOrderEntity getPlusOutOrder(PlusOutOrderQueryReq req);

    /**
     * 获取会员月卡续费计划信息
     */
    PlusRenewalPlanInfoEntity getRenewalPlanInfo(PlusRenewalPlanInfoEvent event);
}
