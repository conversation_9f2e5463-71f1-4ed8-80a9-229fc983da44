package com.juzifenqi.plus.module.order.application.impl;

import com.juzifenqi.plus.dto.req.order.PlusOrderQueryReq;
import com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq;
import com.juzifenqi.plus.module.order.application.IPlusOrderQueryApplication;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewInfoAo;
import com.juzifenqi.plus.module.order.application.ao.PlusRenewalPlanInfoAo;
import com.juzifenqi.plus.module.order.application.converter.IPlusOrderApplicationConverter;
import com.juzifenqi.plus.module.order.model.PlusOrderQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderRelationEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewInfoEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRenewalPlanInfoEntity;
import com.juzifenqi.plus.module.order.model.event.PlusRenewInfoEvent;
import com.juzifenqi.plus.module.order.model.event.PlusRenewalPlanInfoEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/23 11:35
 */
@Service
@Slf4j
public class PlusOrderQueryApplicationImpl implements IPlusOrderQueryApplication {

    @Autowired
    private PlusOrderQueryModel plusOrderQueryModel;

    private final IPlusOrderApplicationConverter converter = IPlusOrderApplicationConverter.instance;

    @Override
    public PlusRenewInfoAo getRenewInfo(PlusRenewInfoEvent event) {
        PlusRenewInfoEntity renewInfo = plusOrderQueryModel.getRenewInfo(event);
        return converter.toPlusRenewInfoAo(renewInfo);
    }

    @Override
    public List<PlusOrderRelationEntity> getOrderRelation(PlusOrderRelationEvent event) {
        if (event.getOrderType() == 1) {
            return plusOrderQueryModel.getRelationByPlusOrderSn(event.getOrderSns(),
                    event.getBusinessType());
        }
        return plusOrderQueryModel.getRelationByLoanOrderSn(event.getOrderSns(),
                event.getBusinessType());
    }

    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderList(Integer userId, Integer channelId,
            Integer programId) {
        return plusOrderQueryModel.getUserWaitPayOrderList(userId, channelId, programId);
    }

    /**
     * 根据条件查询用户订单列表
     */
    @Override
    public List<PlusOrderEntity> getUserPlusOrderList(PlusOrderQueryReq req) {
        return plusOrderQueryModel.getUserPlusOrderList(req);
    }

    /**
     * 获取待支付后付款订单信息
     */
    @Override
    public List<PlusOrderEntity> getUserWaitPayOrderList(PlusOrderQueryReq req) {
        return plusOrderQueryModel.getUserWaitPayOrderList(req);
    }

    @Override
    public void deductError(Integer beforeMinutes, Integer warningNumber) {
        plusOrderQueryModel.deductError(beforeMinutes, warningNumber);
    }

    @Override
    public PlusOrderEntity getUserOutPlusOrder(PlusOutOrderQueryReq req) {
        return plusOrderQueryModel.getPlusOutOrder(req);
    }

    @Override
    public PlusRenewalPlanInfoAo getRenewalPlanInfo(PlusRenewalPlanInfoEvent event) {
        PlusRenewalPlanInfoEntity renewInfo = plusOrderQueryModel.getRenewalPlanInfo(event);
        return converter.toPlusRenewalPlanInfoAo(renewInfo);
    }
}
