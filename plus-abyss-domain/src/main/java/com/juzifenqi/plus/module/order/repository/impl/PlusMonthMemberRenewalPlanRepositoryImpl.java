package com.juzifenqi.plus.module.order.repository.impl;

import com.github.pagehelper.PageHelper;
import com.juzifenqi.plus.module.order.model.contract.IPlusMonthMemberRenewalPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusMonthMemberRenewalPlanEntity;
import com.juzifenqi.plus.module.order.repository.converter.IPlusMonthMemberRenewalPlanRepositoryConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPlusMonthMemberRenewalPlanMapper;
import com.juzifenqi.plus.module.order.repository.po.PlusMonthMemberRenewalPlanPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 会员月卡续费计划仓储实现类
 *
 * <AUTHOR>
 * @date 2025-8-18
 */
@Repository
@Slf4j
public class PlusMonthMemberRenewalPlanRepositoryImpl implements IPlusMonthMemberRenewalPlanRepository {

    @Autowired
    private IPlusMonthMemberRenewalPlanMapper mapper;

    private final IPlusMonthMemberRenewalPlanRepositoryConverter converter = 
            IPlusMonthMemberRenewalPlanRepositoryConverter.instance;

    @Override
    public Integer save(PlusMonthMemberRenewalPlanEntity entity) {
        if (entity == null) {
            return null;
        }
        PlusMonthMemberRenewalPlanPo po = converter.toPo(entity);
        mapper.save(po);
        return po.getId();
    }

    @Override
    public Integer batchInsert(List<PlusMonthMemberRenewalPlanEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return 0;
        }
        List<PlusMonthMemberRenewalPlanPo> pos = converter.toPoList(entities);
        return mapper.batchInsert(pos);
    }

    @Override
    public List<PlusMonthMemberRenewalPlanEntity> getByOrderSn(String orderSn) {
        if (orderSn == null || orderSn.trim().isEmpty()) {
            return Collections.emptyList();
        }
        List<PlusMonthMemberRenewalPlanPo> pos = mapper.getByOrderSn(orderSn);
        return converter.toEntityList(pos);
    }

    @Override
    public List<PlusMonthMemberRenewalPlanEntity> getByOrderSnAndStates(String orderSn, List<Integer> planStates) {
        if (orderSn == null || orderSn.trim().isEmpty()) {
            return Collections.emptyList();
        }
        List<PlusMonthMemberRenewalPlanPo> pos = mapper.getByOrderSnAndStates(orderSn, planStates);
        return converter.toEntityList(pos);
    }

    @Override
    public List<PlusMonthMemberRenewalPlanEntity> pageQuery(Date planTime, List<Integer> planStates, 
                                                            Integer pageNum, Integer pageSize) {
        // 使用PageHelper进行分页
        if (pageNum != null && pageSize != null) {
            PageHelper.startPage(pageNum, pageSize);
        }
        
        List<PlusMonthMemberRenewalPlanPo> pos = mapper.pageQuery(planTime, planStates);
        return converter.toEntityList(pos);
    }

    @Override
    public Integer countQuery(Date planTime, List<Integer> planStates) {
        return mapper.countQuery(planTime, planStates);
    }

    @Override
    public PlusMonthMemberRenewalPlanEntity getById(Integer id) {
        if (id == null) {
            return null;
        }
        PlusMonthMemberRenewalPlanPo po = mapper.getById(id);
        return converter.toEntity(po);
    }

    @Override
    public Boolean updatePlanState(Integer id, Integer planState, String remark) {
        if (id == null || planState == null) {
            return false;
        }
        return mapper.updatePlanState(id, planState, remark) > 0;
    }

    @Override
    public Boolean updatePlanStateAndActualTime(Integer id,String orderSn, Integer planState, String remark, Date actualPlanTime) {
        return mapper.updatePlanStateAndActualTime(id, orderSn, planState, remark, actualPlanTime) > 0;
    }

    @Override
    public void cancelRenewalPlan(String orderSn) {
        mapper.cancelRenewalPlan(orderSn);
    }
}
