<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.juzifenqi</groupId>
        <artifactId>plus-abyss</artifactId>
        <version>ykd-1.1.2-SNAPSHOT</version>
    </parent>

    <artifactId>plus-abyss-config</artifactId>
    <version>ykd-1.0.0-SNAPSHOT</version>

    <dependencies>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>

        <!--工具类groot-->
        <dependency>
            <groupId>com.groot.utils</groupId>
            <artifactId>groot-all</artifactId>
            <version>1.0.4.7-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>rocket-mq</artifactId>
            <version>1.0.4-RELEASE</version>
        </dependency>

        <!-- dubbo -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo</artifactId>
            <version>3.2.16</version>
        </dependency>

        <!-- zookeeper -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper</artifactId>
            <version>3.2.16</version>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-reload4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.101tec</groupId>
            <artifactId>zkclient</artifactId>
            <version>0.9</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--nacos -->
        <dependency>
            <groupId>com.alibaba.nacos</groupId>
            <artifactId>nacos-client</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>0.2.12</version>
        </dependency>

        <!--优惠券服务-->
        <dependency>
            <artifactId>coupon-api</artifactId>
            <groupId>com.juzifenqi</groupId>
            <version>2.0.6-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.juzifenqi.product</groupId>
                    <artifactId>product-service-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>jzfq_auth</groupId>
            <artifactId>auth_api</artifactId>
            <version>3.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--额度-->
        <dependency>
            <groupId>com.juzishuke</groupId>
            <artifactId>credit-api</artifactId>
            <version>1.0.8-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi.sorder</groupId>
            <artifactId>super-order-service-client</artifactId>
            <version>2.0.10</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>aplha-api</artifactId>
            <version>ykd-1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>galaxy-plus</groupId>
                    <artifactId>plus-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--用户中心-->
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>member-platform-api</artifactId>
            <version>2.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>jzfq-sms-server</groupId>
            <artifactId>sms-api</artifactId>
            <version>1.3.15-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--商品服务-->
        <dependency>
            <groupId>com.juzifenqi.product</groupId>
            <artifactId>product-service-client</artifactId>
            <version>1.1.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>alita-dubbo-client</artifactId>
                    <groupId>com.juzifenqi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.juzifenqi.product</groupId>
            <artifactId>product-search-service</artifactId>
            <version>0.0.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>alita-dubbo-client</artifactId>
                    <groupId>com.juzifenqi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.juzishuke.framework</groupId>
            <artifactId>unified-encrypt-client-boot</artifactId>
            <version>1.2.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>fastjson</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- 虚拟商品权益 -->
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>virtual-api</artifactId>
            <version>ykd-1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>virtual-bean</artifactId>
            <version>ykd-1.0.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.juzifenqi.trade</groupId>
            <artifactId>mall-trade-client</artifactId>
            <version>1.0.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.juzifenqi</groupId>
                    <artifactId>alita-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.projectlombok</groupId>
                    <artifactId>lombok</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--橡树黑卡-->
        <dependency>
            <groupId>com.oakvip</groupId>
            <artifactId>sdk-core</artifactId>
            <version>1.1.3</version>
        </dependency>

        <!--核心-->
<!--        <dependency>-->
<!--            <groupId>com.juzifenqi</groupId>-->
<!--            <artifactId>acm-api</artifactId>-->
<!--            <version>0.7.5-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>*</artifactId>-->
<!--                    <groupId>com.alibaba</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <!--工单-->
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>work-order-api</artifactId>
            <version>0.1.5-RELEASE</version>
        </dependency>

        <!--支持-->
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>support-api</artifactId>
            <version>ykd-1.0.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.juzishuke</groupId>
            <artifactId>contract-api</artifactId>
            <version>1.3.318-SNAPSHOT</version>
        </dependency>

        <!--分销-->
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>online-shop-api</artifactId>
            <version>1.4.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>online-shop-bean</artifactId>
            <version>1.4.5-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>magic-api</artifactId>
        </dependency>

        <dependency>
            <groupId>activity</groupId>
            <artifactId>activity-rpc</artifactId>
            <version>1.0.8.5-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>taker-api</artifactId>
            <version>0.1.16.SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>taker-bean</artifactId>
            <version>0.1.16.SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>*</artifactId>
                    <groupId>*</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.jzsk</groupId>
            <artifactId>rms-vps-api</artifactId>
            <version>3.0-SNAPSHOT</version>
        </dependency>

        <!--支付-->
<!--        <dependency>-->
<!--            <groupId>com.juzishuke</groupId>-->
<!--            <artifactId>pay-open-api</artifactId>-->
<!--            <version>1.1.29.open-GA</version>-->
<!--        </dependency>-->
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
